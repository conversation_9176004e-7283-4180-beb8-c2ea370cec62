<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Tagihan Pola - {{ $record->nomor_invoice }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }

        .company-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border: 2px solid #2563eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
        }

        .company-logo img {
            max-width: 70px;
            max-height: 70px;
            object-fit: contain;
        }

        .company-logo-fallback {
            font-size: 10px;
            font-weight: bold;
            text-align: center;
            color: #2563eb;
            line-height: 1.2;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }

        .company-tagline {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 3px;
            font-style: italic;
        }

        .company-services {
            font-size: 10px;
            color: #64748b;
        }

        .invoice-title {
            text-align: right;
        }

        .invoice-title h1 {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }

        .invoice-number {
            font-size: 14px;
            color: #374151;
            margin-bottom: 5px;
        }

        .invoice-type-badge {
            background: #7c3aed;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 10px;
            font-weight: bold;
            display: inline-block;
        }

        /* Customer & Invoice Details */
        .details-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }

        .customer-details, .invoice-details {
            flex: 1;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 8px;
        }

        .detail-label {
            font-weight: bold;
            width: 120px;
            color: #374151;
        }

        .detail-value {
            flex: 1;
            color: #111827;
        }

        /* Items Table */
        .items-section {
            margin-bottom: 30px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .items-table th {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            color: #374151;
            font-size: 11px;
        }

        .items-table td {
            border: 1px solid #d1d5db;
            padding: 10px 8px;
            font-size: 11px;
            color: #111827;
        }

        .items-table .text-center {
            text-align: center;
        }

        .items-table .text-right {
            text-align: right;
        }

        /* Tagihan Pola Specific Styling */
        .tagihan-pola-note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 11px;
            color: #92400e;
        }

        .tagihan-pola-note .note-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #b45309;
        }

        /* Summary Section */
        .summary-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }

        .summary-table {
            width: 300px;
            border-collapse: collapse;
        }

        .summary-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .summary-table .summary-label {
            font-weight: bold;
            color: #374151;
            text-align: left;
        }

        .summary-table .summary-value {
            text-align: right;
            color: #111827;
        }

        .summary-table .total-row {
            background: #f3f4f6;
            font-weight: bold;
            font-size: 14px;
            border-top: 2px solid #2563eb;
            border-bottom: 2px solid #2563eb;
        }

        .summary-table .total-row td {
            color: #1e40af;
        }

        /* Footer */
        .footer-section {
            margin-top: 40px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }

        .signature-box {
            text-align: center;
            width: 200px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 60px;
            color: #374151;
        }

        .signature-line {
            border-bottom: 1px solid #374151;
            margin-bottom: 5px;
        }

        .signature-name {
            font-size: 11px;
            color: #6b7280;
        }

        /* Print Styles */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .invoice-container {
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-section">
                <div class="company-logo {{ empty($logoBase64) ? 'company-logo-fallback' : '' }}">
                    @if (!empty($logoBase64))
                        <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo">
                    @else
                        LINTAS<br>RIAU<br>PRIMA
                    @endif
                </div>
                <div class="company-info">
                    <div class="company-name">PT. LINTAS RIAU PRIMA</div>
                    <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                    <div class="company-services">Fuel Agent - Fuel Transportation - Bunker Service</div>
                </div>
            </div>
            
            <div class="invoice-title">
                <h1>INVOICE</h1>
                <div class="invoice-number">{{ $record->nomor_invoice }}</div>
                <div class="invoice-type-badge">TAGIHAN POLA</div>
            </div>
        </div>

        <!-- Tagihan Pola Notice -->
        <div class="tagihan-pola-note">
            <div class="note-title">INVOICE TAGIHAN POLA</div>
            <div>Invoice ini menggunakan format tagihan pola dengan total amount yang telah disepakati. Detail item dan perhitungan menggunakan sistem paket.</div>
        </div>

        <!-- Customer & Invoice Details -->
        <div class="details-section">
            <div class="customer-details">
                <div class="section-title">DETAIL PELANGGAN</div>
                <div class="detail-row">
                    <div class="detail-label">Nama:</div>
                    <div class="detail-value">{{ $record->nama_pelanggan }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Alamat:</div>
                    <div class="detail-value">{{ $record->alamat_pelanggan }}</div>
                </div>
                @if($record->npwp_pelanggan)
                <div class="detail-row">
                    <div class="detail-label">NPWP:</div>
                    <div class="detail-value">{{ $record->npwp_pelanggan }}</div>
                </div>
                @endif
            </div>
            
            <div class="invoice-details">
                <div class="section-title">DETAIL INVOICE</div>
                <div class="detail-row">
                    <div class="detail-label">Tanggal:</div>
                    <div class="detail-value">{{ $record->tanggal_invoice->format('d/m/Y') }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Jatuh Tempo:</div>
                    <div class="detail-value">{{ $record->tanggal_jatuh_tempo->format('d/m/Y') }}</div>
                </div>
                @if($record->transaksiPenjualan)
                <div class="detail-row">
                    <div class="detail-label">No. SO:</div>
                    <div class="detail-value">{{ $record->transaksiPenjualan->kode ?? '-' }}</div>
                </div>
                @endif
            </div>
        </div>

        <!-- Items Section -->
        <div class="items-section">
            <div class="section-title">DETAIL TAGIHAN</div>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 5%;">No</th>
                        <th style="width: 45%;">Deskripsi Layanan</th>
                        <th style="width: 10%;" class="text-center">Qty</th>
                        <th style="width: 10%;" class="text-center">Satuan</th>
                        <th style="width: 15%;" class="text-right">Total Amount</th>
                        @if($record->invoiceItems->where('include_ppn', true)->count() > 0)
                        <th style="width: 15%;" class="text-right">Total + PPN</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    @foreach($record->invoiceItems as $index => $item)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $item->item_name }}</td>
                        <td class="text-center">{{ number_format($item->quantity, 0) }}</td>
                        <td class="text-center">{{ $item->unit }}</td>
                        <td class="text-right">Rp {{ number_format($item->total_amount, 0, ',', '.') }}</td>
                        @if($record->invoiceItems->where('include_ppn', true)->count() > 0)
                        <td class="text-right">
                            @if($item->include_ppn)
                                Rp {{ number_format($item->total_amount + $item->ppn_amount, 0, ',', '.') }}
                            @else
                                Rp {{ number_format($item->total_amount, 0, ',', '.') }}
                            @endif
                        </td>
                        @endif
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Summary Section -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="summary-label">Subtotal:</td>
                    <td class="summary-value">Rp {{ number_format($record->subtotal, 0, ',', '.') }}</td>
                </tr>
                @if($record->total_pajak > 0)
                <tr>
                    <td class="summary-label">PPN (11%):</td>
                    <td class="summary-value">Rp {{ number_format($record->total_pajak, 0, ',', '.') }}</td>
                </tr>
                @endif
                <tr class="total-row">
                    <td class="summary-label">TOTAL INVOICE:</td>
                    <td class="summary-value">Rp {{ number_format($record->total_invoice, 0, ',', '.') }}</td>
                </tr>
            </table>
        </div>

        <!-- Footer Section -->
        <div class="footer-section">
            @if($record->catatan)
            <div style="margin-bottom: 20px;">
                <strong>Catatan:</strong><br>
                {{ $record->catatan }}
            </div>
            @endif
            
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-title">Hormat Kami,</div>
                    <div class="signature-line"></div>
                    <div class="signature-name">PT. LINTAS RIAU PRIMA</div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-title">Diterima Oleh,</div>
                    <div class="signature-line"></div>
                    <div class="signature-name">{{ $record->nama_pelanggan }}</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
