<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Aset extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aset';

    protected $fillable = [
        'kode_aset',
        'nama_aset',
        'deskripsi_aset',
        'kategori_aset',
        'nilai_aset',
        'useful_life_months',
        'salvage_value',
        'depreciation_start_date',
        'last_depreciation_date',
        'is_depreciable',
        'depreciation_notes',
        'tanggal_akuisisi',
        'id_akun_aset',
        'id_akun_depresiasi',
        'id_akun_akuisisi',
        'created_by',
    ];

    protected $casts = [
        'nilai_aset' => 'decimal:2',
        'salvage_value' => 'decimal:2',
        'useful_life_months' => 'integer',
        'is_depreciable' => 'boolean',
        'tanggal_akuisisi' => 'date',
        'depreciation_start_date' => 'date',
        'last_depreciation_date' => 'date',
    ];

    // Relationships
    public function akunAset()
    {
        return $this->belongsTo(Akun::class, 'id_akun_aset');
    }

    public function akunDepresiasi()
    {
        return $this->belongsTo(Akun::class, 'id_akun_depresiasi');
    }

    public function akunAkuisisi()
    {
        return $this->belongsTo(Akun::class, 'id_akun_akuisisi');
    }

    // Asset Categories
    const ASSET_CATEGORIES = [
        'Tanah' => 'Tanah',
        'Bangunan' => 'Bangunan',
        'Peralatan' => 'Peralatan',
        'Transportasi' => 'Transportasi',
        'Lainnya' => 'Lainnya',
    ];

    public function depreciations()
    {
        return $this->hasMany(AsetDepreciation::class, 'aset_id')->latest();
    }

    public function accumulatedDepreciations()
    {
        return $this->depreciations()->sum('amount');
    }

    public function mustBeDepreciated()
    {
        return $this->nilai_aset - $this->salvage_value;
    }

    // Helper Methods
    public function calculateDepreciation()
    {
        if (!$this->is_depreciable || $this->kategori_aset === 'Tanah') {
            return 0;
        }

        $depreciableAmount = $this->mustBeDepreciated();

        return $depreciableAmount / $this->useful_life_months;
    }

    public function calculateCurrentBookValue()
    {
        return $this->nilai_aset - $this->accumulatedDepreciations();
    }

    public function getYearsElapsed()
    {
        if (!$this->depreciation_start_date) {
            return 0;
        }

        return now()->diffInYears($this->depreciation_start_date);
    }

    public function getMonthsElapsed()
    {
        if (!$this->depreciation_start_date) {
            return 0;
        }

        return now()->diffInMonths($this->depreciation_start_date);
    }

    public function processDepreciation()
    {
        if (!$this->is_depreciable || $this->kategori_aset === 'Tanah') {
            return [
                "message" => "Aset ini tidak dapat didepresiasi.",
                "amount" => 0
            ];
        }

        $monthlyDepreciation = $this->calculateDepreciation();

        if ($this->last_depreciation_date && $this->last_depreciation_date->format('Y-m') == now()->format('Y-m')) {
            return [
                "message" => "Depresiasi untuk bulan ini sudah diproses.",
                "amount" => $monthlyDepreciation
            ];
        }

        if ($this->accumulatedDepreciations() + $monthlyDepreciation > $this->mustBeDepreciated()) {
            $monthlyDepreciation = $this->mustBeDepreciated() - $this->accumulatedDepreciations();
        }

        if ($monthlyDepreciation > 0) {
            $this->depreciations()->create([
                'depreciation_date' => now(),
                'amount' => $monthlyDepreciation,
            ]);
            $this->last_depreciation_date = now();
            $this->save();

            $journal = Journal::create([
                'transaction_date' => now(),
                'reference_number' => $this->kode_aset,
                'source_type' => class_basename($this),
                'description' => 'Depresiasi Aset: ' . $this->nama_aset . ' - ' . $this->kode_aset,
                'status' => 'Posted',
                'created_by' => $this->created_by,
            ]);

            $journal->journalEntries()->createMany([
                [
                    'account_id' => $this->id_akun_aset,
                    'debit' => 0,
                    'credit' => $monthlyDepreciation,
                    'description' => 'Kredit Akun Aset: ' . $this->nama_aset,
                ],
                [
                    'account_id' => $this->id_akun_depresiasi,
                    'debit' => $monthlyDepreciation,
                    'credit' => 0,
                    'description' => 'Debit Beban Depresiasi Aset: ' . $this->nama_aset,
                ],
            ]);

            return [
                "message" => "Aset berhasil didepresiasi sebesar Rp " . number_format($monthlyDepreciation, 2, ',', '.'),
                "amount" => $monthlyDepreciation
            ];
        }

        return [
            "message" => "Tidak ada depresiasi yang perlu diproses.",
            "amount" => 0
        ];
    }

    public function isFullyDepreciated()
    {
        return $this->accumulatedDepreciations() >= $this->mustBeDepreciated();
    }

    public function getRemainingDepreciableAmount()
    {
        return max(0, $this->mustBeDepreciated() - $this->accumulatedDepreciations());
    }

    public function getDepreciationPercentage()
    {
        if ($this->nilai_aset == 0) return 0;
        return ($this->accumulatedDepreciations() / $this->nilai_aset) * 100;
    }

    // Scopes
    public function scopeDepreciable($query)
    {
        return $query->where('is_depreciable', true)->where('kategori_aset', '!=', 'Tanah');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('kategori_aset', $category);
    }

    public function getLastDepreciationDateFormattedAttribute()
    {
        return $this->last_depreciation_date ? $this->last_depreciation_date->format('d F Y') : 'Belum Ada';
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($asset) {
            // Set depreciation start date if not set
            if (!$asset->depreciation_start_date && $asset->tanggal_akuisisi) {
                $asset->depreciation_start_date = $asset->tanggal_akuisisi;
            }
        });
    }

    protected static function booted()
    {
        static::created(function ($asset) {
            $journal = Journal::create([
                'transaction_date' => now(),
                'reference_number' => $asset->kode_aset,
                'source_type' => class_basename($asset),
                'description' => 'Pencatatan Aset: ' . $asset->nama_aset . ' - ' . $asset->kode_aset,
                'status' => 'Posted',
                'created_by' => $asset->created_by,
            ]);

            $journal->journalEntries()->createMany([
                [
                    'account_id' => $asset->id_akun_aset,
                    'debit' => $asset->nilai_aset,
                    'credit' => 0,
                    'description' => 'Debit Aset: ' . $asset->nama_aset,
                ],
                [
                    'account_id' => $asset->id_akun_akuisisi,
                    'debit' => 0,
                    'credit' => $asset->nilai_aset,
                    'description' => 'Kredit untuk pembelian aset: ' . $asset->nama_aset,
                ],
            ]);
        });
    }
}
