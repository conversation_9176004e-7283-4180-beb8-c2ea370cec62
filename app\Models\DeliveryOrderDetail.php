<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryOrderDetail extends Model
{
    protected $table = 'delivery_order_details';

    protected $fillable = [
        'id_delivery_order',
        'id_penjualan_detail',
        'id_item',
        'item_name',
        'item_description',
        'volume_ordered',
        'volume_delivered',
        'unit',
        'notes',
    ];

    protected $casts = [
        'volume_ordered' => 'decimal:2',
        'volume_delivered' => 'decimal:2',
    ];

    protected static function booted(): void
    {
        static::creating(function (DeliveryOrderDetail $detail) {
            // Ensure required fields have values
            if (empty($detail->volume_ordered) && $detail->penjualanDetail) {
                $detail->volume_ordered = $detail->penjualanDetail->volume_item;
            }

            if (empty($detail->volume_delivered)) {
                $detail->volume_delivered = $detail->volume_ordered ?? 0;
            }



            if (empty($detail->item_name) && $detail->item) {
                $detail->item_name = $detail->item->name;
            }

            if (empty($detail->unit)) {
                $detail->unit = 'Liter';
            }
        });



        static::saved(function (DeliveryOrderDetail $detail) {
            // Update parent delivery order volume
            $detail->updateDeliveryOrderVolume();
        });

        static::deleted(function (DeliveryOrderDetail $detail) {
            // Update parent delivery order volume
            $detail->updateDeliveryOrderVolume();
        });
    }

    /**
     * Update parent delivery order total volume
     */
    public function updateDeliveryOrderVolume(): void
    {
        if ($this->deliveryOrder) {
            $totalVolume = $this->deliveryOrder->details()->sum('volume_delivered');
            $this->deliveryOrder->update(['volume_do' => $totalVolume]);
        }
    }

    /**
     * Get the delivery order that owns the detail
     */
    public function deliveryOrder(): BelongsTo
    {
        return $this->belongsTo(DeliveryOrder::class, 'id_delivery_order');
    }

    /**
     * Get the penjualan detail that this DO detail is based on
     */
    public function penjualanDetail(): BelongsTo
    {
        return $this->belongsTo(PenjualanDetail::class, 'id_penjualan_detail');
    }

    /**
     * Get the item
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(Item::class, 'id_item');
    }

    /**
     * Get remaining volume that can be delivered
     */
    public function getRemainingVolumeAttribute(): float
    {
        return $this->volume_ordered - $this->volume_delivered;
    }

    /**
     * Get delivery percentage
     */
    public function getDeliveryPercentageAttribute(): float
    {
        if ($this->volume_ordered <= 0) {
            return 0;
        }

        return ($this->volume_delivered / $this->volume_ordered) * 100;
    }
}
