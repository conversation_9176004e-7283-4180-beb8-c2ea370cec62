<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\Pelanggan;
use App\Models\Tbbm;
use App\Models\Sph;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;

class SalesMarketingDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-presentation-chart-line';
    protected static ?string $navigationLabel = 'Sales & Marketing';
    protected static ?string $title = 'Dashboard Sales & Marketing';
    protected static string $view = 'filament.pages.sales-marketing-dashboard';
    protected static ?int $navigationSort = 8;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]


    public ?string $selectedPeriod = null;
    #[Url(keep: true)]

    public ?string $selectedTipe = null;
    #[Url(keep: true)]

    public ?string $selectedTbbm = null;
    public $startDate = null;
    public $endDate = null;

    public static function canAccess(): bool
    {
        return true; // Adjust based on your permission system
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_month';
        $this->startDate = $this->startDate ?? now()->startOfMonth()->format('Y-m-d');
        $this->endDate = $this->endDate ?? now()->endOfMonth()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_month' => 'Bulan Ini',
                        'last_month' => 'Bulan Lalu',
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_month')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                Select::make('selectedTipe')
                    ->label('Tipe Penjualan (Opsional)')
                    ->options([
                        'dagang' => 'Dagang',
                        'jasa' => 'Jasa',
                    ])
                    ->placeholder('Semua Tipe')
                    ->live(),

                Select::make('selectedTbbm')
                    ->label('TBBM (Opsional)')
                    ->options(Tbbm::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua TBBM')
                    ->live(),
            ])
            ->columns(5);
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();

        match ($period) {
            'current_month' => [
                $this->startDate = $now->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'last_month' => [
                $this->startDate = $now->subMonth()->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'current_quarter' => [
                $this->startDate = $now->startOfQuarter()->format('Y-m-d'),
                $this->endDate = $now->endOfQuarter()->format('Y-m-d')
            ],
            'current_year' => [
                $this->startDate = $now->startOfYear()->format('Y-m-d'),
                $this->endDate = $now->endOfYear()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getSalesMarketingKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // Sales Funnel Analysis
        $totalSph = Sph::whereBetween('created_at', [$startDate, $endDate])->count();

        $totalSO = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])
            ->when($this->selectedTipe, fn($q) => $q->where('tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('id_tbbm', $this->selectedTbbm))
            ->count();

        $conversionRate = $totalSph > 0 ? round(($totalSO / $totalSph) * 100, 1) : 0;

        // Customer Metrics
        $newCustomers = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at')
            ->whereRaw('transaksi_penjualan.tanggal = (
                SELECT MIN(tp2.tanggal)
                FROM transaksi_penjualan tp2
                WHERE tp2.id_pelanggan = transaksi_penjualan.id_pelanggan
                AND tp2.deleted_at IS NULL
            )')
            ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->distinct('transaksi_penjualan.id_pelanggan')
            ->count();

        $activeCustomers = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])
            ->when($this->selectedTipe, fn($q) => $q->where('tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('id_tbbm', $this->selectedTbbm))
            ->distinct('id_pelanggan')
            ->count();

        // Revenue Metrics
        $totalRevenue = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('penjualan_detail.deleted_at')
            ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        $avgOrderValue = $totalSO > 0 ? $totalRevenue / $totalSO : 0;

        // Territory Performance
        $territoryCount = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])
            ->when($this->selectedTipe, fn($q) => $q->where('tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('id_tbbm', $this->selectedTbbm))
            ->distinct('id_tbbm')
            ->count();

        return [
            'total_sph' => $totalSph,
            'total_so' => $totalSO,
            'conversion_rate' => $conversionRate,
            'new_customers' => $newCustomers,
            'active_customers' => $activeCustomers,
            'total_revenue' => $totalRevenue,
            'avg_order_value' => $avgOrderValue,
            'territory_count' => $territoryCount,
        ];
    }

    public function getCustomerAcquisitionData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at')
            ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->select([
                DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m") as month'),
                DB::raw('COUNT(DISTINCT CASE WHEN transaksi_penjualan.tanggal = (
                    SELECT MIN(tp2.tanggal)
                    FROM transaksi_penjualan tp2
                    WHERE tp2.id_pelanggan = transaksi_penjualan.id_pelanggan
                    AND tp2.deleted_at IS NULL
                ) THEN transaksi_penjualan.id_pelanggan END) as new_customers'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id_pelanggan) as total_customers'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            ])
            ->groupBy(DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m")'))
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    public function getTopCustomersData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at')
            ->whereNull('penjualan_detail.deleted_at')
            ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->select([
                'pelanggan.nama as customer_name',
                'pelanggan.type as customer_type',
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
                DB::raw('AVG(penjualan_detail.volume_item * penjualan_detail.harga_jual) as avg_order_value'),
                DB::raw('SUM(penjualan_detail.volume_item) as total_volume'),
            ])
            ->groupBy('pelanggan.id', 'pelanggan.nama', 'pelanggan.type')
            ->orderBy('total_revenue', 'desc')
            ->limit(15)
            ->get()
            ->toArray();
    }

    public function getSalesFunnelData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $sphCount = Sph::whereBetween('created_at', [$startDate, $endDate])->count();

        $soCount = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])
            ->when($this->selectedTipe, fn($q) => $q->where('tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('id_tbbm', $this->selectedTbbm))
            ->count();

        $deliveredCount = DB::table('delivery_order')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->where('delivery_order.status_muat', 'selesai')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->count();

        return [
            'sph' => $sphCount,
            'so' => $soCount,
            'delivered' => $deliveredCount,
            'sph_to_so_rate' => $sphCount > 0 ? round(($soCount / $sphCount) * 100, 1) : 0,
            'so_to_delivery_rate' => $soCount > 0 ? round(($deliveredCount / $soCount) * 100, 1) : 0,
        ];
    }

    public function getTerritoryPerformanceData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('transaksi_penjualan')
            ->join('tbbms', 'transaksi_penjualan.id_tbbm', '=', 'tbbms.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('penjualan_detail.deleted_at')
            ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->select([
                'tbbms.nama as territory_name',
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id_pelanggan) as unique_customers'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
                DB::raw('AVG(penjualan_detail.volume_item * penjualan_detail.harga_jual) as avg_order_value'),
            ])
            ->groupBy('tbbms.id', 'tbbms.nama')
            ->orderBy('total_revenue', 'desc')
            ->get()
            ->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedStartDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedEndDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedTipe(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedTbbm(): void
    {
        $this->dispatch('refresh-charts');
    }
}
