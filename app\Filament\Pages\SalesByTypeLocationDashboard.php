<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\Pelanggan;
use App\Models\Tbbm;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;

class SalesByTypeLocationDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Penjualan per Tipe & Lokasi';
    protected static ?string $title = 'Dashboard Penjualan Berdasarkan Tipe & Lokasi';
    protected static string $view = 'filament.pages.sales-by-type-location-dashboard';
    protected static ?int $navigationSort = 5;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]


    public ?string $selectedPeriod = null;
    #[Url(keep: true)]

    public ?string $selectedTipe = null;
    #[Url(keep: true)]

    public ?string $selectedTbbm = null;
    #[Url(keep: true)]

    public ?string $selectedCustomer = null;
    public $startDate = null;
    public $endDate = null;

    public array $data = [];

    public static function canAccess(): bool
    {
        return true; // Adjust based on your permission system
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_year';
        $this->startDate = $this->startDate ?? now()->startOfYear()->format('Y-m-d');
        $this->endDate = $this->endDate ?? now()->endOfYear()->format('Y-m-d');

        // Initialize form data
        $this->data = [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_month' => 'Bulan Ini',
                        'last_month' => 'Bulan Lalu',
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_year')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                Select::make('selectedTipe')
                    ->label('Tipe Penjualan (Opsional)')
                    ->options([
                        'dagang' => 'Dagang',
                        'jasa' => 'Jasa',
                    ])
                    ->placeholder('Semua Tipe')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedTipe = $state;
                        // Force full page refresh untuk memastikan chart ter-update
                        $this->redirect(route('filament.admin.pages.sales-by-type-location-dashboard', [
                            'selectedPeriod' => $this->selectedPeriod,
                            'selectedTipe' => $this->selectedTipe,
                            'selectedTbbm' => $this->selectedTbbm,
                            'selectedCustomer' => $this->selectedCustomer,
                        ]));
                    }),

                Select::make('selectedTbbm')
                    ->label('TBBM (Opsional)')
                    ->options(Tbbm::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua TBBM')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedTbbm = $state;
                        // Force full page refresh untuk memastikan chart ter-update
                        $this->redirect(route('filament.admin.pages.sales-by-type-location-dashboard', [
                            'selectedPeriod' => $this->selectedPeriod,
                            'selectedTipe' => $this->selectedTipe,
                            'selectedTbbm' => $this->selectedTbbm,
                            'selectedCustomer' => $this->selectedCustomer,
                        ]));
                    }),

                Select::make('selectedCustomer')
                    ->label('Pelanggan (Opsional)')
                    ->options(Pelanggan::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua Pelanggan')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedCustomer = $state;
                        // Force full page refresh untuk memastikan chart ter-update
                        $this->redirect(route('filament.admin.pages.sales-by-type-location-dashboard', [
                            'selectedPeriod' => $this->selectedPeriod,
                            'selectedTipe' => $this->selectedTipe,
                            'selectedTbbm' => $this->selectedTbbm,
                            'selectedCustomer' => $this->selectedCustomer,
                        ]));
                    }),
            ])
            ->columns(6)
            ->statePath('data');
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();

        match ($period) {
            'current_month' => [
                $this->startDate = $now->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'last_month' => [
                $this->startDate = $now->subMonth()->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'current_quarter' => [
                $this->startDate = $now->startOfQuarter()->format('Y-m-d'),
                $this->endDate = $now->endOfQuarter()->format('Y-m-d')
            ],
            'current_year' => [
                $this->startDate = $now->startOfYear()->format('Y-m-d'),
                $this->endDate = $now->endOfYear()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getSalesKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $baseQuery = TransaksiPenjualan::query()
            ->whereBetween('tanggal', [$startDate, $endDate]);

        // Apply filters
        if ($this->selectedTipe) {
            $baseQuery->where('tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $baseQuery->where('id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $baseQuery->where('id_pelanggan', $this->selectedCustomer);
        }

        // Total Sales Orders
        $totalSO = $baseQuery->count();

        // Sales by Type
        $dagangSales = (clone $baseQuery)->where('tipe', 'dagang')->count();
        $jasaSales = (clone $baseQuery)->where('tipe', 'jasa')->count();

        // Total Revenue
        $totalRevenue = $baseQuery->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        // Revenue by Type
        $dagangRevenue = TransaksiPenjualan::query()
            ->whereBetween('tanggal', [$startDate, $endDate])
            ->where('tipe', 'dagang')
            ->when($this->selectedTbbm, fn($q) => $q->where('id_tbbm', $this->selectedTbbm))
            ->when($this->selectedCustomer, fn($q) => $q->where('id_pelanggan', $this->selectedCustomer))
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        $jasaRevenue = TransaksiPenjualan::query()
            ->whereBetween('tanggal', [$startDate, $endDate])
            ->where('tipe', 'jasa')
            ->when($this->selectedTbbm, fn($q) => $q->where('id_tbbm', $this->selectedTbbm))
            ->when($this->selectedCustomer, fn($q) => $q->where('id_pelanggan', $this->selectedCustomer))
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        return [
            'total_so' => $totalSO,
            'dagang_sales' => $dagangSales,
            'jasa_sales' => $jasaSales,
            'total_revenue' => $totalRevenue,
            'dagang_revenue' => $dagangRevenue,
            'jasa_revenue' => $jasaRevenue,
            'dagang_percentage' => $totalSO > 0 ? round(($dagangSales / $totalSO) * 100, 1) : 0,
            'jasa_percentage' => $totalSO > 0 ? round(($jasaSales / $totalSO) * 100, 1) : 0,
        ];
    }

    public function getSalesByLocationData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('transaksi_penjualan')
            ->join('tbbms', 'transaksi_penjualan.id_tbbm', '=', 'tbbms.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        // Apply filters
        if ($this->selectedTipe) {
            $query->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        return $query->select([
            'tbbms.nama as location_name',
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
            DB::raw('COUNT(CASE WHEN transaksi_penjualan.tipe = "dagang" THEN 1 END) as dagang_count'),
            DB::raw('COUNT(CASE WHEN transaksi_penjualan.tipe = "jasa" THEN 1 END) as jasa_count'),
        ])
            ->groupBy('tbbms.id', 'tbbms.nama')
            ->orderBy('total_revenue', 'desc')
            ->get()
            ->toArray();
    }

    public function getSalesTrendData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        // Apply filters
        if ($this->selectedTipe) {
            $query->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        return $query->select([
            DB::raw('DATE(transaksi_penjualan.tanggal) as date'),
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
            DB::raw('COUNT(CASE WHEN transaksi_penjualan.tipe = "dagang" THEN 1 END) as dagang_orders'),
            DB::raw('COUNT(CASE WHEN transaksi_penjualan.tipe = "jasa" THEN 1 END) as jasa_orders'),
        ])
            ->groupBy(DB::raw('DATE(transaksi_penjualan.tanggal)'))
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->updateDateRange($this->selectedPeriod);
        $this->dispatch('refresh-charts');
    }

    public function updatedStartDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedEndDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedTipe(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedTbbm(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedCustomer(): void
    {
        $this->dispatch('refresh-charts');
    }

    // Method to refresh data manually
    public function refreshData()
    {
        $this->dispatch('refresh-charts');
    }
}
