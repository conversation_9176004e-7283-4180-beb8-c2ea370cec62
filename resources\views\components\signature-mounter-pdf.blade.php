{{--
    This is a reusable partial view for displaying a signature mounted on a stamp in a PDF.
    It expects a '$signer' variable to be passed to it.
--}}

<div style="position: relative; height: 70px; width: 220px; margin: 8px auto;">
    {{-- The company stamp/logo is the base layer --}}
    @php $stampPath = public_path('storage/business-logos/lrp-logo-noBG.png'); @endphp
    @if (file_exists($stampPath))
        <img src="data:image/png;base64,{{ base64_encode(file_get_contents($stampPath)) }}" alt="Stamp"
            style="position: absolute;
                    left: -6%;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 120px;
                    z-index: 1;">
    @endif

    {{-- The user's signature is the top layer, positioned over the stamp --}}
    @if ($signer && $signer->signature_path)
        @php $signaturePath = public_path('storage/' . $signer->signature_path); @endphp
        @if (file_exists($signaturePath))
            <img src="data:image/png;base64,{{ base64_encode(file_get_contents($signaturePath)) }}" alt="Signature"
                style="position: absolute;
                        left: 30px;      /* Adjust this value to control the overlap */
                        top: 50%;
                        transform: translateY(-50%);
                        width: 200px;    /* Adjust signature width */
                        z-index: 10;">
        @endif
    @else
        {{-- If no signature, create an empty space --}}
        <div style="height: 60px;"></div>
    @endif
</div>
