<?php

namespace App\Filament\Resources\DeliveryOrderResource\RelationManagers;

use App\Models\DeliveryOrderDetail;
use App\Models\PenjualanDetail;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;

class DetailsRelationManager extends RelationManager
{
    protected static string $relationship = 'details';

    protected static ?string $title = 'Detail Item Pengiriman';

    protected static ?string $modelLabel = 'Detail Item';

    protected static ?string $pluralModelLabel = 'Detail Items';

    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Item')
                    ->schema([
                        Forms\Components\Select::make('id_penjualan_detail')
                            ->label('Item dari Transaksi')
                            ->options(function () {
                                $transaksiId = $this->ownerRecord->id_transaksi;
                                if (!$transaksiId) return [];

                                return PenjualanDetail::where('id_transaksi_penjualan', $transaksiId)
                                    ->with('item')
                                    ->get()
                                    ->mapWithKeys(function ($detail) {
                                        return [$detail->id => $detail->item->name . ' - ' . number_format($detail->volume_item, 0, ',', '.') . ' L'];
                                    });
                            })
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $penjualanDetail = PenjualanDetail::with('item.satuan')->find($state);
                                    if ($penjualanDetail) {
                                        $set('id_item', $penjualanDetail->id_item);
                                        $set('item_name', $penjualanDetail->item->name);
                                        $set('item_description', $penjualanDetail->item->description);
                                        $set('volume_ordered', $penjualanDetail->volume_item);
                                        $set('unit', $penjualanDetail->item->satuan->nama ?? 'Liter');

                                        // Set default volume_delivered to volume_ordered
                                        $set('volume_delivered', $penjualanDetail->volume_item);
                                    }
                                }
                            })
                            ->required(),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('item_name')
                                    ->label('Nama Item')
                                    ->disabled()
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('volume_ordered')
                                    ->label('Volume Dipesan')
                                    ->numeric()
                                    ->suffix('L')
                                    ->disabled()
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('volume_delivered')
                                    ->label('Volume Dikirim')
                                    ->numeric()
                                    ->suffix('L')
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                                        // Validate that delivered volume doesn't exceed ordered volume
                                        $volumeOrdered = floatval($get('volume_ordered'));
                                        $volumeDelivered = floatval($state);

                                        if ($volumeDelivered > $volumeOrdered) {
                                            Notification::make()
                                                ->title('Volume dikirim tidak boleh melebihi volume dipesan')
                                                ->warning()
                                                ->send();

                                            $set('volume_delivered', $volumeOrdered);
                                        }
                                    }),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('unit')
                                    ->label('Satuan')
                                    ->disabled()
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('item_description')
                                    ->label('Deskripsi Item')
                                    ->disabled()
                                    ->dehydrated(),
                            ]),

                        Forms\Components\Textarea::make('notes')
                            ->label('Alamat')
                            ->rows(3)
                            ->placeholder('Alamat khusus untuk item ini')
                            ->columnSpanFull(),

                        // Hidden fields
                        Forms\Components\Hidden::make('id_item'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('item_name')
            ->columns([
                Tables\Columns\TextColumn::make('item.name')
                    ->label('Item')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('volume_ordered')
                    ->label('Volume Dipesan')
                    ->numeric(decimalPlaces: 0)
                    ->suffix(' L')
                    ->sortable(),

                Tables\Columns\TextColumn::make('volume_delivered')
                    ->label('Volume Dikirim')
                    ->numeric(decimalPlaces: 0)
                    ->suffix(' L')
                    ->sortable()
                    ->color('success'),

                Tables\Columns\TextColumn::make('delivery_percentage')
                    ->label('Persentase')
                    ->getStateUsing(function ($record) {
                        if ($record->volume_ordered <= 0) return '0%';
                        return number_format(($record->volume_delivered / $record->volume_ordered) * 100, 1) . '%';
                    })
                    ->badge()
                    ->color(function ($record) {
                        $percentage = ($record->volume_delivered / max($record->volume_ordered, 1)) * 100;
                        if ($percentage >= 100) return 'success';
                        if ($percentage >= 80) return 'warning';
                        return 'danger';
                    }),

                Tables\Columns\TextColumn::make('remaining_volume')
                    ->label('Sisa Volume')
                    ->getStateUsing(function ($record) {
                        $remaining = $record->volume_ordered - $record->volume_delivered;
                        return number_format($remaining, 0) . ' L';
                    })
                    ->color(function ($record) {
                        $remaining = $record->volume_ordered - $record->volume_delivered;
                        return $remaining > 0 ? 'warning' : 'success';
                    }),

                Tables\Columns\TextColumn::make('unit')
                    ->label('Satuan')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('notes')
                    ->label('Catatan')
                    ->limit(30)
                    ->placeholder('Tidak ada catatan')
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('incomplete_delivery')
                    ->label('Pengiriman Belum Lengkap')
                    ->query(fn($query) => $query->whereRaw('volume_delivered < volume_ordered')),

                Tables\Filters\Filter::make('complete_delivery')
                    ->label('Pengiriman Lengkap')
                    ->query(fn($query) => $query->whereRaw('volume_delivered >= volume_ordered')),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->after(function () {
                        $this->updateDeliveryOrderTotals();

                        Notification::make()
                            ->title('Detail item berhasil ditambahkan')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->after(function () {
                        $this->updateDeliveryOrderTotals();
                    }),

                Tables\Actions\DeleteAction::make()
                    ->after(function () {
                        $this->updateDeliveryOrderTotals();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->after(function () {
                            $this->updateDeliveryOrderTotals();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'asc');
    }

    /**
     * Update delivery order totals after detail changes
     */
    protected function updateDeliveryOrderTotals(): void
    {
        $deliveryOrder = $this->ownerRecord;

        // Recalculate total volume
        $totalVolume = $deliveryOrder->details()->sum('volume_delivered');

        // Update delivery order
        $deliveryOrder->update([
            'volume_do' => $totalVolume,
        ]);

        // Optionally recalculate remaining volume from SO
        if ($deliveryOrder->transaksi) {
            $totalSoVolume = $deliveryOrder->transaksi->penjualanDetails()->sum('volume_item');
            $allDeliveredVolume = $deliveryOrder->transaksi->deliveryOrders()
                ->where('delivery_order.id', '!=', $deliveryOrder->id)
                ->join('delivery_order_details', 'delivery_order.id', '=', 'delivery_order_details.id_delivery_order')
                ->sum('delivery_order_details.volume_delivered');

            $remainingVolume = $totalSoVolume - $allDeliveredVolume - $totalVolume;

            $deliveryOrder->update([
                'sisa_volume_do' => max(0, $remainingVolume),
            ]);
        }
    }
}
