<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\DeliveryOrder;
use App\Models\Pelanggan;
use App\Models\Kendaraan;
use App\Models\Invoice;
use App\Models\InvoicePayment;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;

class ExecutiveSummaryDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';
    protected static ?string $navigationLabel = 'Executive Summary';
    protected static ?string $title = 'Executive Summary Dashboard';
    protected static string $view = 'filament.pages.executive-summary-dashboard';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]
    public ?string $selectedPeriod = null;

    public array $data = [];

    public static function canAccess(): bool
    {
        return true;
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_year';
        $this->data['selectedPeriod'] = $this->selectedPeriod;
        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'today' => 'Hari Ini',
                        'current_week' => 'Minggu Ini',
                        'current_month' => 'Bulan Ini',
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                    ])
                    ->default('current_year')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedPeriod = $state;
                        $this->dispatch('refresh-charts');
                    }),
            ])
            ->columns(1)
            ->statePath('data');
    }

    public function getDateRange(): array
    {
        $now = Carbon::now();

        return match ($this->selectedPeriod) {
            'today' => [$now->copy()->startOfDay(), $now->copy()->endOfDay()],
            'current_week' => [$now->copy()->startOfWeek(), $now->copy()->endOfWeek()],
            'current_month' => [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()],
            'current_quarter' => [$now->copy()->startOfQuarter(), $now->copy()->endOfQuarter()],
            'current_year' => [$now->copy()->startOfYear(), $now->copy()->endOfYear()],
            default => [$now->copy()->startOfYear(), $now->copy()->endOfYear()],
        };
    }

    public function getExecutiveKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // Revenue from Invoices (total invoice amount)
        $totalRevenue = Invoice::whereBetween('tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('total_invoice')
            ->sum('total_invoice');

        // Previous period for comparison
        $previousPeriod = $this->getPreviousPeriodRange();
        $previousRevenue = Invoice::whereBetween('tanggal_invoice', $previousPeriod)
            ->whereNotNull('total_invoice')
            ->sum('total_invoice');

        $revenueGrowth = $previousRevenue > 0 ? round((($totalRevenue - $previousRevenue) / $previousRevenue) * 100, 1) : 0;

        // Cash Flow (actual payments received)
        $totalCashReceived = InvoicePayment::whereBetween('payment_date', [$startDate, $endDate])
            ->where('status', 'accepted')
            ->sum('amount');

        $previousCashReceived = InvoicePayment::whereBetween('payment_date', $previousPeriod)
            ->where('status', 'accepted')
            ->sum('amount');

        $cashGrowth = $previousCashReceived > 0 ? round((($totalCashReceived - $previousCashReceived) / $previousCashReceived) * 100, 1) : 0;

        // Outstanding Receivables
        $outstandingReceivables = Invoice::where('sisa_tagihan', '>', 0)
            ->sum('sisa_tagihan');

        // Sales Orders
        $totalSO = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])->count();
        $previousSO = TransaksiPenjualan::whereBetween('tanggal', $previousPeriod)->count();
        $soGrowth = $previousSO > 0 ? round((($totalSO - $previousSO) / $previousSO) * 100, 1) : 0;

        // Deliveries
        $totalDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])->count();
        $completedDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')->count();
        $deliveryRate = $totalDeliveries > 0 ? round(($completedDeliveries / $totalDeliveries) * 100, 1) : 0;

        // Customer Metrics
        $activeCustomers = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])
            ->distinct('id_pelanggan')->count();
        $totalCustomers = Pelanggan::count();

        // Operational Metrics
        $activeVehicles = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->distinct('id_kendaraan')->count();
        $totalVehicles = Kendaraan::count();
        $vehicleUtilization = $totalVehicles > 0 ? round(($activeVehicles / $totalVehicles) * 100, 1) : 0;

        // Average Order Value
        $avgOrderValue = $totalSO > 0 ? $totalRevenue / $totalSO : 0;

        return [
            'total_revenue' => $totalRevenue,
            'revenue_growth' => $revenueGrowth,
            'total_cash_received' => $totalCashReceived,
            'cash_growth' => $cashGrowth,
            'outstanding_receivables' => $outstandingReceivables,
            'total_so' => $totalSO,
            'so_growth' => $soGrowth,
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'delivery_rate' => $deliveryRate,
            'active_customers' => $activeCustomers,
            'total_customers' => $totalCustomers,
            'active_vehicles' => $activeVehicles,
            'total_vehicles' => $totalVehicles,
            'vehicle_utilization' => $vehicleUtilization,
            'avg_order_value' => $avgOrderValue,
        ];
    }

    public function getPreviousPeriodRange(): array
    {
        $now = Carbon::now();

        return match ($this->selectedPeriod) {
            'today' => [$now->copy()->subDay()->startOfDay(), $now->copy()->subDay()->endOfDay()],
            'current_week' => [$now->copy()->subWeek()->startOfWeek(), $now->copy()->subWeek()->endOfWeek()],
            'current_month' => [$now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()],
            'current_quarter' => [$now->copy()->subQuarter()->startOfQuarter(), $now->copy()->subQuarter()->endOfQuarter()],
            'current_year' => [$now->copy()->subYear()->startOfYear(), $now->copy()->subYear()->endOfYear()],
            default => [$now->copy()->subYear()->startOfYear(), $now->copy()->subYear()->endOfYear()],
        };
    }

    public function getRevenueByTypeData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('invoice.total_invoice')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->select([
                'transaksi_penjualan.tipe',
                DB::raw('COUNT(DISTINCT invoice.id) as total_invoices'),
                DB::raw('SUM(invoice.total_invoice) as total_revenue'),
                DB::raw('SUM(invoice.total_terbayar) as total_paid'),
                DB::raw('SUM(invoice.sisa_tagihan) as total_outstanding'),
            ])
            ->groupBy('transaksi_penjualan.tipe')
            ->get()
            ->toArray();
    }

    public function getMonthlyTrendData(): array
    {
        $endDate = Carbon::now();
        $startDate = $endDate->copy()->subMonths(11)->startOfMonth();

        // Get actual data from database
        $actualData = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('invoice.total_invoice')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->select([
                DB::raw('DATE_FORMAT(invoice.tanggal_invoice, "%Y-%m") as month'),
                DB::raw('COUNT(DISTINCT invoice.id) as total_invoices'),
                DB::raw('SUM(invoice.total_invoice) as total_revenue'),
                DB::raw('SUM(invoice.total_terbayar) as total_paid'),
            ])
            ->groupBy(DB::raw('DATE_FORMAT(invoice.tanggal_invoice, "%Y-%m")'))
            ->orderBy('month')
            ->get()
            ->keyBy('month');

        // Generate all 12 months
        $result = [];
        for ($i = 11; $i >= 0; $i--) {
            $monthKey = $endDate->copy()->subMonths($i)->format('Y-m');
            $monthLabel = $endDate->copy()->subMonths($i)->format('M Y');

            $result[] = [
                'month' => $monthLabel,
                'total_invoices' => $actualData->get($monthKey)->total_invoices ?? 0,
                'total_revenue' => $actualData->get($monthKey)->total_revenue ?? 0,
                'total_paid' => $actualData->get($monthKey)->total_paid ?? 0,
            ];
        }

        return $result;
    }

    public function getTopPerformingTbbmData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('tbbms', 'transaksi_penjualan.id_tbbm', '=', 'tbbms.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('invoice.total_invoice')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->select([
                'tbbms.nama as tbbm_name',
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
                DB::raw('SUM(invoice.total_invoice) as total_revenue'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id_pelanggan) as unique_customers'),
                DB::raw('SUM(invoice.total_terbayar) as total_paid'),
                DB::raw('ROUND((SUM(invoice.total_terbayar) / SUM(invoice.total_invoice)) * 100, 1) as collection_rate'),
            ])
            ->groupBy('tbbms.id', 'tbbms.nama')
            ->orderBy('total_revenue', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    public function getOperationalSummaryData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $onTimeDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')
            ->whereRaw('DATE(waktu_selesai_muat) <= DATE(tanggal_delivery)')
            ->count();

        $totalCompletedDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')
            ->count();

        $onTimeRate = $totalCompletedDeliveries > 0 ? round(($onTimeDeliveries / $totalCompletedDeliveries) * 100, 1) : 0;

        $avgDeliveryTime = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')
            ->whereNotNull('waktu_muat')
            ->whereNotNull('waktu_selesai_muat')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, waktu_muat, waktu_selesai_muat)) as avg_time')
            ->value('avg_time') ?? 0;

        return [
            'on_time_deliveries' => $onTimeDeliveries,
            'total_completed_deliveries' => $totalCompletedDeliveries,
            'on_time_rate' => $onTimeRate,
            'avg_delivery_time' => round($avgDeliveryTime, 1),
        ];
    }

    public function updatedSelectedPeriod()
    {
        $this->data['selectedPeriod'] = $this->selectedPeriod;
        $this->dispatch('refresh-charts');
    }

    public function refreshData()
    {
        $this->dispatch('$refresh');
    }
}
