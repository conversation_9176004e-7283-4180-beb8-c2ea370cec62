<?php

namespace App\Filament\Resources\TransaksiJasaResource\Pages;

use App\Filament\Resources\TransaksiJasaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;

class ViewTransaksiJasa extends ViewRecord
{
    protected static string $resource = TransaksiJasaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Transaksi Jasa')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('kode')
                                    ->label('Nomor SO')
                                    ->icon('heroicon-o-document-text')
                                    ->color('primary')
                                    ->weight('bold'),

                                TextEntry::make('tanggal')
                                    ->label('Tanggal Transaksi')
                                    ->date('d/m/Y H:i')
                                    ->icon('heroicon-o-calendar'),

                                TextEntry::make('pelanggan.nama')
                                    ->label('Pelanggan')
                                    ->icon('heroicon-o-user')
                                    ->color('success'),

                                TextEntry::make('nomor_po')
                                    ->label('Nomor PO')
                                    ->placeholder('Tidak ada PO')
                                    ->icon('heroicon-o-document'),

                                TextEntry::make('nomor_sph')
                                    ->label('Nomor SPH')
                                    ->placeholder('Tidak ada SPH')
                                    ->icon('heroicon-o-document-text'),
                            ]),
                    ])
                    ->columns(2),

                Section::make('Informasi Pelanggan')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('pelanggan.kode')
                                    ->label('Kode Pelanggan')
                                    ->icon('heroicon-o-identification'),

                                TextEntry::make('pelanggan.type')
                                    ->label('Tipe Pelanggan')
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        'individual' => 'info',
                                        'corporate' => 'success',
                                        default => 'gray'
                                    }),

                                TextEntry::make('pelanggan.pic_nama')
                                    ->label('PIC')
                                    ->icon('heroicon-o-user-circle'),

                                TextEntry::make('pelanggan.pic_phone')
                                    ->label('Telepon PIC')
                                    ->icon('heroicon-o-phone'),

                                TextEntry::make('alamatPelanggan.alamat')
                                    ->label('Alamat Pelanggan')
                                    ->icon('heroicon-o-map-pin')
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columns(2),

                Section::make('Detail Jasa')
                    ->schema([
                        TextEntry::make('penjualanDetails')
                            ->label('Daftar Jasa')
                            ->listWithLineBreaks()
                            ->formatStateUsing(function ($record) {
                                return $record->penjualanDetails->map(function ($detail) {
                                    $total = number_format($detail->volume_item * $detail->harga_jual, 0, ',', '.');
                                    return "• {$detail->item->name} - Qty: {$detail->volume_item} - Harga: IDR " . number_format($detail->harga_jual, 0, ',', '.') . " - Total: IDR {$total}";
                                })->implode("\n");
                            })
                            ->columnSpanFull(),
                    ]),

                Section::make('Ringkasan Keuangan')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('total_jasa')
                                    ->label('Total Jasa')
                                    ->formatStateUsing(function ($record) {
                                        return $record->penjualanDetails->count() . ' jasa';
                                    })
                                    ->badge()
                                    ->color('info'),

                                TextEntry::make('total_kuantitas')
                                    ->label('Total Kuantitas')
                                    ->formatStateUsing(function ($record) {
                                        return number_format($record->penjualanDetails->sum('volume_item'), 0, ',', '.');
                                    })
                                    ->badge()
                                    ->color('warning'),

                                TextEntry::make('total_transaksi')
                                    ->label('Total Transaksi')
                                    ->formatStateUsing(function ($record) {
                                        $total = $record->penjualanDetails->sum(function ($detail) {
                                            return $detail->volume_item * $detail->harga_jual;
                                        });
                                        return 'IDR ' . number_format($total, 0, ',', '.');
                                    })
                                    ->badge()
                                    ->color('success')
                                    ->size('lg'),
                            ]),
                    ])
                    ->columns(3),

                Section::make('Status Transaksi')
                    ->schema([
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'draft' => 'gray',
                                'pending' => 'warning',
                                'approved' => 'success',
                                'rejected' => 'danger',
                                default => 'gray'
                            }),
                    ]),

                Section::make('Informasi Sistem')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('createdBy.name')
                                    ->label('Dibuat Oleh')
                                    ->icon('heroicon-o-user'),

                                TextEntry::make('created_at')
                                    ->label('Dibuat Pada')
                                    ->dateTime('d/m/Y H:i')
                                    ->icon('heroicon-o-clock'),

                                TextEntry::make('updated_at')
                                    ->label('Terakhir Diupdate')
                                    ->dateTime('d/m/Y H:i')
                                    ->icon('heroicon-o-arrow-path'),
                            ]),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    public function getTitle(): string
    {
        return 'Detail Transaksi Jasa';
    }
}
