<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationSetting;
use App\Models\User;
use App\Services\NotificationTemplateService;
use Illuminate\Support\Facades\Log;

class CleanNotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Creates notification settings ONLY for Super Administrator with complete templates.
     */
    public function run(): void
    {
        Log::info('Starting CleanNotificationSeeder...');

        // Cari hanya Super Administrator
        $superAdmin = User::whereHas('roles', function ($query) {
            $query->where('name', 'super_admin');
        })
            ->whereNotNull('hp')
            ->where('hp', '!=', '')
            ->first();

        if (!$superAdmin) {
            $this->command->error('❌ Super Administrator with phone number not found!');
            return;
        }

        // Get all available templates
        $allTemplates = NotificationTemplateService::getDefaultTemplates();
        
        // Add expense approval per divisi templates
        $expenseApprovalTemplates = [
            'expense_manager_update_direksi' => [
                'template' => "💵 *Approval Permintaan Biaya - Direksi*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya dari divisi *Direksi* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nRequester: *{requester_name}*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\nDivisi: *Direksi*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi approval expense untuk divisi Direksi'
            ],
            'expense_manager_update_sales' => [
                'template' => "💵 *Approval Permintaan Biaya - Sales*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya dari divisi *Sales* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nRequester: *{requester_name}*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\nDivisi: *Sales*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi approval expense untuk divisi Sales'
            ],
            'expense_manager_update_operasional' => [
                'template' => "💵 *Approval Permintaan Biaya - Operasional*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya dari divisi *Operasional* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nRequester: *{requester_name}*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\nDivisi: *Operasional*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi approval expense untuk divisi Operasional'
            ],
            'expense_manager_update_administrasi' => [
                'template' => "💵 *Approval Permintaan Biaya - Administrasi*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya dari divisi *Administrasi* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nRequester: *{requester_name}*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\nDivisi: *Administrasi*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi approval expense untuk divisi Administrasi'
            ],
            'expense_manager_update_keuangan' => [
                'template' => "💵 *Approval Permintaan Biaya - Keuangan*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya dari divisi *Keuangan* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nRequester: *{requester_name}*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\nDivisi: *Keuangan*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi approval expense untuk divisi Keuangan'
            ],
            'expense_manager_update_hrd' => [
                'template' => "💵 *Approval Permintaan Biaya - HRD*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya dari divisi *HRD* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nRequester: *{requester_name}*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\nDivisi: *HRD*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi approval expense untuk divisi HRD'
            ],
            'expense_manager_update_it' => [
                'template' => "💵 *Approval Permintaan Biaya - IT*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya dari divisi *IT* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nRequester: *{requester_name}*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\nDivisi: *IT*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi approval expense untuk divisi IT'
            ],
            'sph_manager_update_sales' => [
                'template' => "📋 *SPH Baru dari Sales*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda SPH baru dari divisi *Sales* yang membutuhkan persetujuan Anda.\n\n📝 *Detail SPH:*\nCreator: *{creator_name}*\nNo. SPH: *{sph_number}*\nPelanggan: *{customer_name}*\nTotal: *Rp {total_amount}*\nDivisi: *Sales*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'creator_name', 'sph_number', 'customer_name', 'total_amount', 'view_url'],
                'description' => 'Notifikasi SPH baru dari divisi Sales'
            ],
            'expense_approved_for_finance' => [
                'template' => "✅ *Expense Disetujui - Notifikasi Finance*\n\nHalo Tim Finance,\nAda expense yang telah disetujui dan perlu diproses pembayaran.\n\n💰 *Detail Expense:*\nNo. Request: *{request_number}*\nRequester: *{requester_name}*\nJumlah: *Rp {approved_amount}*\nDisetujui oleh: *{approver_name}*\n\nMohon segera diproses pembayaran sesuai prosedur.\n\nTerima kasih.",
                'variables' => ['request_number', 'requester_name', 'approved_amount', 'approver_name'],
                'description' => 'Notifikasi ke Finance untuk expense yang disetujui'
            ],
        ];

        // Merge all templates
        $allTemplates = array_merge($allTemplates, $expenseApprovalTemplates);
        
        $createdCount = 0;

        foreach ($allTemplates as $eventName => $templateData) {
            // Create notification setting with template for Super Admin only
            NotificationSetting::create([
                'event_name' => $eventName,
                'user_id' => $superAdmin->id,
                'channel' => 'whatsapp',
                'is_active' => true,
                'message_template' => $templateData['template'],
                'template_variables' => $templateData['variables'],
                'message_preview' => NotificationTemplateService::generatePreview($eventName, $templateData['template']),
            ]);

            $createdCount++;
            Log::info("Created notification setting with template: {$eventName} for Super Admin");
        }

        Log::info("CleanNotificationSeeder completed. Created {$createdCount} notification settings for Super Administrator.");

        // Tampilkan ringkasan
        $this->command->info("✅ Created {$createdCount} notification settings");
        $this->command->info("📱 Configured for Super Administrator:");
        $this->command->info("   - {$superAdmin->name} ({$superAdmin->hp})");

        $this->command->info("🔔 All Events configured:");
        foreach ($allTemplates as $eventName => $templateData) {
            $this->command->info("   - {$eventName}: {$templateData['description']}");
        }

        // Show total
        $totalSettings = NotificationSetting::count();
        $this->command->info("📊 Total notification settings: {$totalSettings}");
    }
}
