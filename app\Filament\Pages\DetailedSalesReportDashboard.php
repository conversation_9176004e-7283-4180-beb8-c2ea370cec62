<?php

namespace App\Filament\Pages;

use App\Models\Pelanggan;
use App\Models\Tbbm;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;

class DetailedSalesReportDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    protected static ?string $navigationLabel = 'Laporan Penjualan Detail';
    protected static ?string $title = 'Dashboard Laporan Penjualan Detail (Neto vs Bruto)';
    protected static string $view = 'filament.pages.detailed-sales-report-dashboard';
    protected static ?int $navigationSort = 6;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]


    public ?string $selectedPeriod = null;
    #[Url(keep: true)]

    public ?string $selectedTipe = null;
    #[Url(keep: true)]

    public ?string $selectedTbbm = null;
    #[Url(keep: true)]

    public ?string $selectedCustomer = null;
    #[Url(keep: true)]

    public ?string $selectedTransaction = null;
    #[Url(keep: true)]

    public ?string $selectedCustomerName = null;
    public $startDate = null;
    public $endDate = null;

    public static function canAccess(): bool
    {
        return true; // Adjust based on your permission system
    }

    public function mount(): void
    {
        // Initialize filters from URL parameters
        $this->selectedPeriod = request('selectedPeriod', 'current_year');
        $this->selectedTipe = request('selectedTipe');
        $this->selectedTbbm = request('selectedTbbm');
        $this->selectedCustomer = request('selectedCustomer');
        $this->selectedTransaction = request('selectedTransaction');
        $this->selectedCustomerName = request('selectedCustomerName');

        $this->updateDateRange($this->selectedPeriod);
    }

    public function getTransactionOptions(): array
    {
        $transactions = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at')
            ->select(
                'transaksi_penjualan.id',
                'transaksi_penjualan.kode',
                'transaksi_penjualan.tipe',
                'transaksi_penjualan.tanggal',
                'pelanggan.nama as customer_name'
            )
            ->orderBy('transaksi_penjualan.created_at', 'desc')
            ->limit(100) // Limit untuk performance
            ->get();

        $options = [];
        foreach ($transactions as $transaction) {
            $kode = $transaction->kode ?: "ID-{$transaction->id}";
            $tanggal = date('d/m/Y', strtotime($transaction->tanggal));
            $tipe = ucfirst($transaction->tipe);
            $options[$transaction->id] = "{$kode} - {$transaction->customer_name} ({$tipe}, {$tanggal})";
        }

        return $options;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_month' => 'Bulan Ini',
                        'last_month' => 'Bulan Lalu',
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_year')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                Select::make('selectedTipe')
                    ->label('Tipe Penjualan (Opsional)')
                    ->options([
                        'dagang' => 'Dagang',
                        'jasa' => 'Jasa',
                    ])
                    ->placeholder('Semua Tipe')
                    ->live(),

                Select::make('selectedTbbm')
                    ->label('TBBM (Opsional)')
                    ->options(Tbbm::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua TBBM')
                    ->live(),

                Select::make('selectedCustomer')
                    ->label('Pelanggan (Opsional)')
                    ->options(Pelanggan::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua Pelanggan')
                    ->live(),

                Select::make('selectedTransaction')
                    ->label('Transaksi (Opsional)')
                    ->options($this->getTransactionOptions())
                    ->searchable()
                    ->placeholder('Pilih transaksi')
                    ->live(),

                TextInput::make('selectedCustomerName')
                    ->label('Nama Pelanggan (Opsional)')
                    ->placeholder('Cari berdasarkan nama pelanggan')
                    ->live(onBlur: true),
            ])
            ->columns(3);
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();

        match ($period) {
            'current_month' => [
                $this->startDate = $now->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'last_month' => [
                $this->startDate = $now->subMonth()->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'current_quarter' => [
                $this->startDate = $now->startOfQuarter()->format('Y-m-d'),
                $this->endDate = $now->endOfQuarter()->format('Y-m-d')
            ],
            'current_year' => [
                $this->startDate = $now->startOfYear()->format('Y-m-d'),
                $this->endDate = $now->endOfYear()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getDetailedSalesKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // Build invoice query with proper filters
        $invoiceQuery = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('invoice.total_invoice')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at');

        // Apply filters through relationships
        if ($this->selectedTipe) {
            $invoiceQuery->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $invoiceQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $invoiceQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTransaction) {
            $invoiceQuery->where('transaksi_penjualan.id', $this->selectedTransaction);
        }

        if ($this->selectedCustomerName) {
            $invoiceQuery->where('pelanggan.nama', 'like', '%' . $this->selectedCustomerName . '%');
        }

        // Get invoice IDs for further calculations
        $invoiceIds = $invoiceQuery->pluck('invoice.id');

        // Revenue Bruto = Total Invoice (actual invoiced amount)
        $grossRevenue = $invoiceQuery->sum('invoice.total_invoice');

        // Get actual deductions from invoice_deductions table
        $deductionQuery = DB::table('invoice_deductions')
            ->whereIn('invoice_id', $invoiceIds)
            ->whereNull('deleted_at');

        $totalDeductions = $deductionQuery->sum('amount');
        $ppnDeductions = DB::table('invoice_deductions')
            ->whereIn('invoice_id', $invoiceIds)
            ->where('deduction_type', 'ppn')
            ->whereNull('deleted_at')
            ->sum('amount');
        $pphDeductions = DB::table('invoice_deductions')
            ->whereIn('invoice_id', $invoiceIds)
            ->where('deduction_type', 'pph')
            ->whereNull('deleted_at')
            ->sum('amount');
        $adminDeductions = DB::table('invoice_deductions')
            ->whereIn('invoice_id', $invoiceIds)
            ->where('deduction_type', 'admin_bank')
            ->whereNull('deleted_at')
            ->sum('amount');
        $losisDeductions = DB::table('invoice_deductions')
            ->whereIn('invoice_id', $invoiceIds)
            ->where('deduction_type', 'losis')
            ->whereNull('deleted_at')
            ->sum('amount');

        // Revenue Neto = Revenue Bruto - Total Deductions (actual)
        $netRevenue = $grossRevenue - $totalDeductions;

        // Get actual payments from invoice_payments table
        $totalPaid = DB::table('invoice_payments')
            ->whereIn('invoice_id', $invoiceIds)
            ->where('status', 'accepted')
            ->whereNull('deleted_at')
            ->sum('amount');

        // Calculate remaining amounts and collection rate
        $remainingAmount = $grossRevenue - $totalPaid;
        $collectionRate = $grossRevenue > 0 ? round(($totalPaid / $grossRevenue) * 100, 2) : 0;

        // Get delivery costs (actual from delivery orders)
        $deliveryCosts = DB::table('delivery_order')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('invoice', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereIn('invoice.id', $invoiceIds)
            ->whereNull('delivery_order.deleted_at')
            ->sum('delivery_order.biaya_sewa_jasa') ?? 0;

        // Calculate profit and margin based on actual data
        $totalCosts = $totalDeductions + $deliveryCosts;
        $profit = $netRevenue - $deliveryCosts;
        $profitMargin = $grossRevenue > 0 ? round(($profit / $grossRevenue) * 100, 2) : 0;

        // Get volume metrics from related sales transactions
        $volumeData = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereIn('invoice.id', $invoiceIds)
            ->whereNull('penjualan_detail.deleted_at')
            ->selectRaw('SUM(penjualan_detail.volume_item) as total_volume')
            ->first();

        $totalVolume = $volumeData->total_volume ?? 0;
        $avgPricePerUnit = $totalVolume > 0 ? $grossRevenue / $totalVolume : 0;

        return [
            'gross_revenue' => $grossRevenue,
            'net_revenue' => $netRevenue,
            'total_deductions' => $totalDeductions,
            'ppn_deductions' => $ppnDeductions,
            'pph_deductions' => $pphDeductions,
            'admin_deductions' => $adminDeductions,
            'losis_deductions' => $losisDeductions,
            'delivery_costs' => $deliveryCosts,
            'total_costs' => $totalCosts,
            'profit' => $profit,
            'profit_margin' => $profitMargin,
            'total_paid' => $totalPaid,
            'remaining_amount' => $remainingAmount,
            'collection_rate' => $collectionRate,
            'total_volume' => $totalVolume,
            'avg_price_per_unit' => $avgPricePerUnit,
            'total_invoices' => count($invoiceIds),
        ];
    }

    public function getRevenueBreakdownData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->leftJoin('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('invoice.total_invoice')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at');

        // Apply filters
        if ($this->selectedTipe) {
            $query->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTransaction) {
            $query->where('transaksi_penjualan.id', $this->selectedTransaction);
        }

        if ($this->selectedCustomerName) {
            $query->where('pelanggan.nama', 'like', '%' . $this->selectedCustomerName . '%');
        }

        $results = $query->select([
            'pelanggan.nama as customer_name',
            'transaksi_penjualan.tipe as sales_type',
            DB::raw('COUNT(DISTINCT invoice.id) as total_invoices'),
            DB::raw('SUM(COALESCE(penjualan_detail.volume_item, 0)) as total_volume'),
            DB::raw('SUM(invoice.total_invoice) as gross_revenue'),
            DB::raw('SUM(invoice.total_terbayar) as total_paid'),
            DB::raw('AVG(CASE WHEN penjualan_detail.volume_item > 0 THEN penjualan_detail.harga_jual ELSE 0 END) as avg_unit_price'),
        ])
            ->groupBy('pelanggan.id', 'pelanggan.nama', 'transaksi_penjualan.tipe')
            ->orderBy('gross_revenue', 'desc')
            ->limit(20)
            ->get();

        // Calculate net revenue for each customer by subtracting actual deductions
        $finalResults = [];
        foreach ($results as $result) {
            // Get all invoice IDs for this customer and type
            $customerInvoiceIds = DB::table('invoice')
                ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
                ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
                ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
                ->where('pelanggan.nama', $result->customer_name)
                ->where('transaksi_penjualan.tipe', $result->sales_type)
                ->whereNotNull('invoice.total_invoice')
                ->whereNull('invoice.deleted_at')
                ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
                ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
                ->when($this->selectedCustomer, fn($q) => $q->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer))
                ->pluck('invoice.id');

            // Calculate total deductions for this customer
            $totalDeductions = DB::table('invoice_deductions')
                ->whereIn('invoice_id', $customerInvoiceIds)
                ->whereNull('deleted_at')
                ->sum('amount');

            // Calculate net revenue
            $netRevenue = $result->gross_revenue - $totalDeductions;

            $finalResults[] = (object) [
                'customer_name' => $result->customer_name,
                'sales_type' => $result->sales_type,
                'total_orders' => $result->total_invoices,
                'total_volume' => $result->total_volume,
                'gross_revenue' => $result->gross_revenue,
                'net_revenue' => $netRevenue,
                'total_paid' => $result->total_paid,
                'total_deductions' => $totalDeductions,
                'avg_unit_price' => $result->avg_unit_price,
            ];
        }

        return $finalResults;
    }

    public function getMonthlyTrendData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->leftJoin('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('invoice.total_invoice')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedTipe) {
            $query->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTransaction) {
            $query->where('transaksi_penjualan.id', $this->selectedTransaction);
        }

        if ($this->selectedCustomerName) {
            $query->where('pelanggan.nama', 'like', '%' . $this->selectedCustomerName . '%');
        }

        $monthlyData = $query->select([
            DB::raw('DATE_FORMAT(invoice.tanggal_invoice, "%Y-%m") as month'),
            DB::raw('SUM(invoice.total_invoice) as gross_revenue'),
            DB::raw('SUM(invoice.total_terbayar) as total_paid'),
            DB::raw('SUM(COALESCE(penjualan_detail.volume_item, 0)) as total_volume'),
            DB::raw('COUNT(DISTINCT invoice.id) as total_invoices'),
        ])
            ->groupBy(DB::raw('DATE_FORMAT(invoice.tanggal_invoice, "%Y-%m")'))
            ->orderBy('month')
            ->get();

        // Calculate net revenue for each month by subtracting actual deductions
        $finalResults = [];
        foreach ($monthlyData as $monthData) {
            // Get all invoice IDs for this month
            $monthInvoiceIds = DB::table('invoice')
                ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
                ->whereRaw('DATE_FORMAT(invoice.tanggal_invoice, "%Y-%m") = ?', [$monthData->month])
                ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
                ->whereNotNull('invoice.total_invoice')
                ->whereNull('invoice.deleted_at')
                ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
                ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
                ->when($this->selectedCustomer, fn($q) => $q->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer))
                ->pluck('invoice.id');

            // Calculate total deductions for this month
            $totalDeductions = DB::table('invoice_deductions')
                ->whereIn('invoice_id', $monthInvoiceIds)
                ->whereNull('deleted_at')
                ->sum('amount');

            // Calculate net revenue
            $netRevenue = $monthData->gross_revenue - $totalDeductions;

            $finalResults[] = [
                'month' => $monthData->month,
                'gross_revenue' => $monthData->gross_revenue,
                'net_revenue' => $netRevenue,
                'total_paid' => $monthData->total_paid,
                'total_deductions' => $totalDeductions,
                'total_volume' => $monthData->total_volume,
                'total_orders' => $monthData->total_invoices,
            ];
        }

        return $finalResults;
    }

    public function getPaymentStatusData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $invoiceQuery = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNotNull('invoice.total_invoice')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedTipe) {
            $invoiceQuery->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $invoiceQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $invoiceQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTransaction) {
            $invoiceQuery->where('transaksi_penjualan.id', $this->selectedTransaction);
        }

        if ($this->selectedCustomerName) {
            $invoiceQuery->where('pelanggan.nama', 'like', '%' . $this->selectedCustomerName . '%');
        }

        return [
            'fully_paid' => (clone $invoiceQuery)->where('invoice.status', 'paid')->count(),
            'partial_paid' => (clone $invoiceQuery)->where('invoice.status', 'partial')->count(),
            'unpaid' => (clone $invoiceQuery)->where('invoice.status', 'sent')->count(),
            'overdue' => (clone $invoiceQuery)->where('invoice.status', 'overdue')->count(),
            'draft' => (clone $invoiceQuery)->where('invoice.status', 'draft')->count(),
        ];
    }

    public function getCashFlowData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $paymentQuery = DB::table('invoice_payments')
            ->join('invoice', 'invoice_payments.invoice_id', '=', 'invoice.id')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice_payments.payment_date', [$startDate, $endDate])
            ->where('invoice_payments.status', 'accepted')
            ->whereNull('invoice_payments.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedTipe) {
            $paymentQuery->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $paymentQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $paymentQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTransaction) {
            $paymentQuery->where('transaksi_penjualan.id', $this->selectedTransaction);
        }

        if ($this->selectedCustomerName) {
            $paymentQuery->where('pelanggan.nama', 'like', '%' . $this->selectedCustomerName . '%');
        }

        return $paymentQuery->select([
            DB::raw('DATE_FORMAT(invoice_payments.payment_date, "%Y-%m") as month'),
            DB::raw('SUM(invoice_payments.amount) as total_received'),
            DB::raw('COUNT(DISTINCT invoice_payments.id) as payment_count'),
            DB::raw('COUNT(DISTINCT invoice.id) as invoice_count'),
        ])
            ->groupBy(DB::raw('DATE_FORMAT(invoice_payments.payment_date, "%Y-%m")'))
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    public function getDeductionBreakdownData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $deductionQuery = DB::table('invoice_deductions')
            ->join('invoice', 'invoice_deductions.invoice_id', '=', 'invoice.id')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNull('invoice_deductions.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedTipe) {
            $deductionQuery->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $deductionQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $deductionQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTransaction) {
            $deductionQuery->where('transaksi_penjualan.id', $this->selectedTransaction);
        }

        if ($this->selectedCustomerName) {
            $deductionQuery->where('pelanggan.nama', 'like', '%' . $this->selectedCustomerName . '%');
        }

        return $deductionQuery->select([
            'invoice_deductions.deduction_type',
            DB::raw('SUM(invoice_deductions.amount) as total_amount'),
            DB::raw('COUNT(invoice_deductions.id) as deduction_count'),
            DB::raw('AVG(invoice_deductions.amount) as avg_amount'),
        ])
            ->groupBy('invoice_deductions.deduction_type')
            ->orderBy('total_amount', 'desc')
            ->get()
            ->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->updateDateRange($this->selectedPeriod);
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }

    public function updatedStartDate(): void
    {
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }

    public function updatedEndDate(): void
    {
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }

    public function updatedSelectedTipe(): void
    {
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }

    public function updatedSelectedTbbm(): void
    {
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }

    public function updatedSelectedCustomer(): void
    {
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }

    public function updatedSelectedTransaction(): void
    {
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }

    public function updatedSelectedCustomerName(): void
    {
        $this->redirect(route('filament.admin.pages.detailed-sales-report-dashboard', [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedTipe' => $this->selectedTipe,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTransaction' => $this->selectedTransaction ?? '',
            'selectedCustomerName' => $this->selectedCustomerName ?? '',
        ]));
    }
}
