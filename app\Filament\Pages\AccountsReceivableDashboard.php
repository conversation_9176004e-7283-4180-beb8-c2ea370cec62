<?php

namespace App\Filament\Pages;

use App\Models\Pelanggan;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;

class AccountsReceivableDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $navigationLabel = ' Piutang Usaha';
    protected static ?string $title = 'Ringkasan Status Piutang Usaha';
    protected static string $view = 'filament.pages.accounts-receivable-dashboard';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]
    public ?string $selectedCustomer = null;

    #[Url(keep: true)]
    public ?string $selectedPeriod = null;

    #[Url(keep: true)]
    public ?string $selectedPaymentStatus = null;

    public array $data = [];

    public static function canAccess(): bool
    {
        return true; // Adjust based on your permission system
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_year';

        // Initialize form data
        $this->data = [
            'selectedPeriod' => $this->selectedPeriod,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedPaymentStatus' => $this->selectedPaymentStatus,
        ];

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_month' => 'Bulan Ini',
                        'last_month' => 'Bulan Lalu',
                        'last_3_months' => '3 Bulan Terakhir',
                        'last_6_months' => '6 Bulan Terakhir',
                        'current_year' => 'Tahun Ini',
                        'all_time' => 'Semua Waktu',
                    ])
                    ->default('current_year')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedPeriod = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedCustomer')
                    ->label('Pelanggan (Opsional)')
                    ->options(Pelanggan::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua Pelanggan')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedCustomer = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedPaymentStatus')
                    ->label('Status Pembayaran (Opsional)')
                    ->options([
                        'sent' => 'Terkirim',
                        'partial' => 'Sebagian',
                        'paid' => 'Lunas',
                        'overdue' => 'Terlambat',
                    ])
                    ->placeholder('Semua Status')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedPaymentStatus = $state;
                        $this->dispatch('refresh-charts');
                    }),
            ])
            ->columns(3)
            ->statePath('data');
    }

    public function getDateRange(): array
    {
        $now = Carbon::now();

        return match ($this->selectedPeriod) {
            'current_month' => [$now->startOfMonth()->copy(), $now->endOfMonth()->copy()],
            'last_month' => [$now->subMonth()->startOfMonth()->copy(), $now->endOfMonth()->copy()],
            'last_3_months' => [$now->subMonths(3)->startOfMonth()->copy(), Carbon::now()->endOfMonth()],
            'last_6_months' => [$now->subMonths(6)->startOfMonth()->copy(), Carbon::now()->endOfMonth()],
            'current_year' => [$now->startOfYear()->copy(), $now->endOfYear()->copy()],
            'all_time' => [Carbon::createFromDate(2020, 1, 1), Carbon::now()->endOfMonth()],
            default => [$now->startOfMonth()->copy(), $now->endOfMonth()->copy()],
        };
    }

    public function getReceivableKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // PERBAIKAN: Menggunakan Invoice sebagai basis piutang (sesuai flow bisnis)
        $baseQuery = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNotIn('invoice.status', ['draft', 'cancelled']); // Hanya invoice yang aktif

        // Apply customer filter
        if ($this->selectedCustomer) {
            $baseQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        // Apply payment status filter
        if ($this->selectedPaymentStatus) {
            $baseQuery->where('invoice.status', $this->selectedPaymentStatus);
        }

        $totalInvoices = $baseQuery->count();
        $paidInvoices = (clone $baseQuery)->where('invoice.status', 'paid')->count();
        $pendingInvoices = (clone $baseQuery)->whereIn('invoice.status', ['sent', 'partial'])->count();
        $overdueInvoices = (clone $baseQuery)->where('invoice.status', 'overdue')->count();

        // PERBAIKAN: Calculate amounts from invoice (lebih akurat)
        $amountQuery = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNotIn('invoice.status', ['draft', 'cancelled']);

        if ($this->selectedCustomer) {
            $amountQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedPaymentStatus) {
            $amountQuery->where('invoice.status', $this->selectedPaymentStatus);
        }

        // Menggunakan field yang sudah ada di invoice
        $totalAmount = $amountQuery->sum('invoice.total_invoice');
        $paidAmount = $amountQuery->sum('invoice.total_terbayar');
        $outstandingAmount = $amountQuery->sum('invoice.sisa_tagihan');

        $collectionRate = $totalAmount > 0 ? round(($paidAmount / $totalAmount) * 100, 1) : 0;

        return [
            'total_invoices' => $totalInvoices,
            'paid_invoices' => $paidInvoices,
            'pending_invoices' => $pendingInvoices,
            'overdue_invoices' => $overdueInvoices,
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount,
            'outstanding_amount' => $outstandingAmount,
            'collection_rate' => $collectionRate,
        ];
    }

    public function getAgingAnalysisData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();
        $now = Carbon::now();

        // PERBAIKAN: Menggunakan Invoice untuk aging analysis
        $receivables = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereIn('invoice.status', ['sent', 'partial', 'overdue']) // Status invoice yang belum lunas
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNotIn('invoice.status', ['draft', 'cancelled'])
            ->when($this->selectedCustomer, function ($query) {
                $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
            })
            ->when($this->selectedPaymentStatus, function ($query) {
                $query->where('invoice.status', $this->selectedPaymentStatus);
            })
            ->select([
                'invoice.tanggal_jatuh_tempo',
                'invoice.status',
                'invoice.sisa_tagihan',
                'invoice.total_invoice'
            ])
            ->get();

        $aging = [
            'current' => ['count' => 0, 'amount' => 0],
            '1_30_days' => ['count' => 0, 'amount' => 0],
            '31_60_days' => ['count' => 0, 'amount' => 0],
            '61_90_days' => ['count' => 0, 'amount' => 0],
            'over_90_days' => ['count' => 0, 'amount' => 0],
        ];

        foreach ($receivables as $receivable) {
            // PERBAIKAN: Menggunakan tanggal_jatuh_tempo dari invoice
            $dueDate = Carbon::parse($receivable->tanggal_jatuh_tempo);
            $daysOverdue = $now->diffInDays($dueDate, false); // false = past dates are negative

            // PERBAIKAN: Menggunakan sisa_tagihan dari invoice
            $amount = $receivable->sisa_tagihan;

            if ($daysOverdue >= 0) {
                // Belum jatuh tempo
                $aging['current']['count']++;
                $aging['current']['amount'] += $amount;
            } elseif ($daysOverdue >= -30) {
                // 1-30 hari terlambat
                $aging['1_30_days']['count']++;
                $aging['1_30_days']['amount'] += $amount;
            } elseif ($daysOverdue >= -60) {
                // 31-60 hari terlambat
                $aging['31_60_days']['count']++;
                $aging['31_60_days']['amount'] += $amount;
            } elseif ($daysOverdue >= -90) {
                // 61-90 hari terlambat
                $aging['61_90_days']['count']++;
                $aging['61_90_days']['amount'] += $amount;
            } else {
                // Lebih dari 90 hari terlambat
                $aging['over_90_days']['count']++;
                $aging['over_90_days']['amount'] += $amount;
            }
        }

        return $aging;
    }

    public function getCustomerRiskAnalysis(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // PERBAIKAN: Menggunakan Invoice untuk customer risk analysis
        return DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNotIn('invoice.status', ['draft', 'cancelled'])
            ->select([
                'pelanggan.nama as customer_name',
                DB::raw('COUNT(DISTINCT invoice.id) as total_invoices'),
                DB::raw('COUNT(DISTINCT CASE WHEN invoice.status = "paid" THEN invoice.id END) as paid_invoices'),
                DB::raw('COUNT(DISTINCT CASE WHEN invoice.status = "overdue" THEN invoice.id END) as overdue_invoices'),
                DB::raw('SUM(invoice.total_invoice) as total_amount'),
                DB::raw('SUM(invoice.total_terbayar) as paid_amount'),
                DB::raw('SUM(invoice.sisa_tagihan) as outstanding_amount'),
                DB::raw('ROUND((SUM(invoice.total_terbayar) / SUM(invoice.total_invoice)) * 100, 1) as payment_rate'),
                DB::raw('CASE
                    WHEN COUNT(DISTINCT CASE WHEN invoice.status = "overdue" THEN invoice.id END) / COUNT(DISTINCT invoice.id) > 0.3 THEN "High"
                    WHEN COUNT(DISTINCT CASE WHEN invoice.status = "overdue" THEN invoice.id END) / COUNT(DISTINCT invoice.id) > 0.1 THEN "Medium"
                    ELSE "Low"
                END as risk_level')
            ])
            ->groupBy('pelanggan.id', 'pelanggan.nama')
            ->orderBy('total_amount', 'desc')
            ->limit(15)
            ->get()
            ->toArray();
    }

    public function getPaymentTrendData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // PERBAIKAN: Get monthly payment trends from invoice
        return DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNotIn('invoice.status', ['draft', 'cancelled'])
            ->when($this->selectedCustomer, function ($query) {
                $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
            })
            ->when($this->selectedPaymentStatus, function ($query) {
                $query->where('invoice.status', $this->selectedPaymentStatus);
            })
            ->select([
                DB::raw('DATE_FORMAT(invoice.tanggal_invoice, "%Y-%m") as month'),
                DB::raw('COUNT(DISTINCT invoice.id) as total_invoices'),
                DB::raw('COUNT(DISTINCT CASE WHEN invoice.status = "paid" THEN invoice.id END) as paid_invoices'),
                DB::raw('SUM(invoice.total_invoice) as total_amount'),
                DB::raw('SUM(invoice.total_terbayar) as paid_amount'),
                DB::raw('SUM(invoice.sisa_tagihan) as outstanding_amount')
            ])
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    public function getTopOverdueCustomers(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // PERBAIKAN: Menggunakan Invoice untuk top overdue customers
        return DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->where('invoice.status', 'overdue')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('pelanggan.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNotIn('invoice.status', ['draft', 'cancelled'])
            ->select([
                'pelanggan.nama as customer_name',
                DB::raw('COUNT(DISTINCT invoice.id) as overdue_count'),
                DB::raw('SUM(invoice.sisa_tagihan) as overdue_amount'),
                DB::raw('MAX(DATEDIFF(NOW(), invoice.tanggal_jatuh_tempo)) as max_days_overdue')
            ])
            ->groupBy('pelanggan.id', 'pelanggan.nama')
            ->orderBy('overdue_amount', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    // ENHANCEMENT: Payment History Analysis
    public function getPaymentHistoryAnalysis(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('invoice_payments')
            ->join('invoice', 'invoice_payments.id_invoice', '=', 'invoice.id')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('invoice_payments.tanggal_pembayaran', [$startDate, $endDate])
            ->whereNull('invoice_payments.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at')
            ->when($this->selectedCustomer, function ($query) {
                $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
            })
            ->select([
                DB::raw('DATE_FORMAT(invoice_payments.tanggal_pembayaran, "%Y-%m") as month'),
                DB::raw('COUNT(*) as payment_count'),
                DB::raw('SUM(invoice_payments.jumlah_pembayaran) as total_payments'),
                DB::raw('AVG(DATEDIFF(invoice_payments.tanggal_pembayaran, invoice.tanggal_invoice)) as avg_payment_days')
            ])
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    // ENHANCEMENT: Invoice Status Distribution
    public function getInvoiceStatusDistribution(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $statusData = DB::table('invoice')
            ->join('transaksi_penjualan', 'invoice.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('invoice.tanggal_invoice', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at')
            ->whereNull('invoice.deleted_at')
            ->whereNotIn('invoice.status', ['draft', 'cancelled'])
            ->when($this->selectedCustomer, function ($query) {
                $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
            })
            ->when($this->selectedPaymentStatus, function ($query) {
                $query->where('invoice.status', $this->selectedPaymentStatus);
            })
            ->select([
                'invoice.status',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(invoice.total_invoice) as total_amount'),
                DB::raw('SUM(invoice.sisa_tagihan) as outstanding_amount')
            ])
            ->groupBy('invoice.status')
            ->get();

        $statusLabels = [
            'sent' => 'Terkirim',
            'partial' => 'Sebagian Terbayar',
            'paid' => 'Lunas',
            'overdue' => 'Terlambat'
        ];

        return $statusData->map(function ($item) use ($statusLabels) {
            return [
                'status' => $statusLabels[$item->status] ?? $item->status,
                'count' => $item->count,
                'total_amount' => $item->total_amount,
                'outstanding_amount' => $item->outstanding_amount,
                'percentage' => 0 // Will be calculated in frontend
            ];
        })->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->dispatch('$refresh');
    }

    public function updatedSelectedCustomer(): void
    {
        $this->dispatch('$refresh');
    }

    public function updatedSelectedPaymentStatus(): void
    {
        $this->dispatch('$refresh');
    }
}
