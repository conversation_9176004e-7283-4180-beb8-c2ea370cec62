<?php

namespace App\Filament\Resources\DeliveryOrderResource\Pages;

use App\Filament\Resources\DeliveryOrderResource;
use App\Models\DeliveryOrder;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Placeholder;
use Filament\Infolists\Components\RepeatableEntry;
use Afsakar\LeafletMapPicker\LeafletMapPickerEntry;
use Filament\Forms\Get;

class ViewDeliveryOrder extends ViewRecord
{
    protected static string $resource = DeliveryOrderResource::class;

    // protected function getHeaderActions(): array
    // {
    //     return [
    //         Actions\EditAction::make(),

    //         Actions\Action::make('view_transaction')
    //             ->label('Lihat Transaksi')
    //             ->icon('heroicon-o-document-text')
    //             ->color('info')
    //             ->url(fn () => route('filament.admin.resources.transaksi-penjualans.view', [
    //                 'record' => $this->record->id_transaksi
    //             ]))
    //             ->visible(fn () => $this->record->id_transaksi)
    //             ->tooltip('Lihat transaksi penjualan terkait'),
    //     ];
    // }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('Preview PDF')
                ->color('gray')
                ->icon('heroicon-o-eye')
                ->action(null)
                ->modalContent(function (DeliveryOrder $record): \Illuminate\View\View {
                    // Load record with all necessary relationships including letterSetting
                    $deliveryOrder = DeliveryOrder::with([
                        'transaksi.pelanggan',
                        'transaksi.alamatPelanggan',
                        'transaksi.penjualanDetails.item.satuan',
                        'transaksi.tbbm',
                        'kendaraan',
                        'user',
                        'uangJalan',
                        'pengirimanDriver',
                        'seals',
                        'letterSetting'
                    ])->find($record->id);

                    // Get logo as base64
                    $logoPath = public_path('images/lrp.png');
                    $logoBase64 = '';

                    if (File::exists($logoPath)) {
                        $logoBase64 = base64_encode(File::get($logoPath));
                    }

                    // Determine the locale from letter setting
                    $locale = $deliveryOrder->letterSetting?->locale ?? 'id';
                    $viewName = "delivery_order.do-preview-{$locale}";

                    // Check if the view exists, fallback to Indonesian if not
                    if (!View::exists($viewName)) {
                        $viewName = 'delivery_order.do-preview';
                    }

                    return View::make($viewName, [
                        'record' => $deliveryOrder,
                        'logoBase64' => $logoBase64
                    ]);
                })
                ->modalHeading("Preview PDF: {$this->record->kode}")
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Tutup')
                ->slideOver()
                ->modalWidth('4xl')
                ->extraModalFooterActions([
                    Actions\Action::make('download_from_modal')
                        ->label('Download PDF')
                        ->color('success')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(function (DeliveryOrder $record) {
                            try {
                                // Load the record with all necessary relationships including letterSetting
                                $deliveryOrder = DeliveryOrder::with([
                                    'transaksi.pelanggan',
                                    'transaksi.alamatPelanggan',
                                    'transaksi.penjualanDetails.item.satuan',
                                    'transaksi.tbbm',
                                    'kendaraan',
                                    'user',
                                    'uangJalan',
                                    'pengirimanDriver',
                                    'seals',
                                    'letterSetting'
                                ])->find($record->id);

                                // Get logo as base64
                                $logoPath = public_path('images/lrp.png');
                                $logoBase64 = '';

                                if (File::exists($logoPath)) {
                                    $logoBase64 = base64_encode(File::get($logoPath));
                                }

                                // Determine the locale from letter setting
                                $locale = $deliveryOrder->letterSetting?->locale ?? 'id';
                                $viewName = "pdf.delivery_order_{$locale}";

                                // Check if the view exists, fallback to Indonesian if not
                                if (!View::exists($viewName)) {
                                    $viewName = 'pdf.delivery_order';
                                }

                                // Generate dynamic filename
                                $filename = 'DO_' . $deliveryOrder->kode . '_' . now()->format('Ymd_His') . '.pdf';

                                // Load the PDF view with the record data
                                $pdf = Pdf::loadView($viewName, [
                                    'record' => $deliveryOrder,
                                    'logoBase64' => $logoBase64
                                ])
                                    ->setPaper('a4', 'portrait')
                                    ->setOptions([
                                        'isHtml5ParserEnabled' => true,
                                        'isPhpEnabled' => true,
                                        'defaultFont' => 'Arial'
                                    ]);

                                // Stream the PDF as a download
                                return response()->streamDownload(function () use ($pdf) {
                                    echo $pdf->output();
                                }, $filename);
                            } catch (\Exception $e) {
                                // Log the error for debugging
                                Log::error('Failed to generate PDF from modal: ' . $e->getMessage());

                                // Show notification to user
                                \Filament\Notifications\Notification::make()
                                    ->title('Error generating PDF')
                                    ->body('Failed to generate PDF: ' . $e->getMessage())
                                    ->danger()
                                    ->send();

                                return null;
                            }
                        })
                ]),

            Actions\EditAction::make(),
            //lihat sales order
            Actions\Action::make('view_transaction')
                ->label('Lihat Transaksi')
                ->icon('heroicon-o-document-text')
                ->color('info')
                ->url(fn() => route('filament.admin.resources.transaksi-penjualans.view', [
                    'record' => $this->record->id_transaksi
                ]))
                ->visible(fn() => $this->record->id_transaksi)
                ->tooltip('Lihat transaksi penjualan terkait'),

            Actions\Action::make('print')
                ->label('Print DO')
                ->icon('heroicon-o-printer')
                ->action(function ($record) {
                    try {
                        // Load the record with all necessary relationships including letterSetting
                        $deliveryOrder = DeliveryOrder::with([
                            'transaksi.pelanggan',
                            'transaksi.alamatPelanggan',
                            'transaksi.penjualanDetails.item.satuan',
                            'kendaraan',
                            'user',
                            'uangJalan',
                            'pengirimanDriver',
                            'seals',
                            'letterSetting'
                        ])->find($record->id);

                        // Get logo as base64
                        $logoPath = public_path('images/lrp.png');
                        $logoBase64 = '';

                        if (File::exists($logoPath)) {
                            $logoBase64 = base64_encode(File::get($logoPath));
                        }

                        // Determine the locale from letter setting
                        $locale = $deliveryOrder->letterSetting?->locale ?? 'id';
                        $viewName = "pdf.delivery_order_{$locale}";

                        // Check if the view exists, fallback to Indonesian if not
                        if (!View::exists($viewName)) {
                            $viewName = 'pdf.delivery_order';
                        }

                        // Generate dynamic filename
                        $filename = 'DO_' . $deliveryOrder->kode . '_' . now()->format('Ymd_His') . '.pdf';

                        // Load the PDF view with the record data
                        $pdf = Pdf::loadView($viewName, ['record' => $deliveryOrder, 'logoBase64' => $logoBase64])
                            ->setPaper('a4', 'portrait')
                            ->setOptions([
                                'isHtml5ParserEnabled' => true,
                                'isPhpEnabled' => true,
                                'defaultFont' => 'Arial'
                            ]);

                        // Stream the PDF as a download
                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf->output();
                        }, $filename);
                    } catch (\Exception $e) {
                        // Log the error for debugging
                        Log::error('Failed to generate PDF: ' . $e->getMessage());
                        Log::error('PDF Error Stack Trace: ' . $e->getTraceAsString());

                        // Show notification to user
                        \Filament\Notifications\Notification::make()
                            ->title('Error generating PDF')
                            ->body('Failed to generate PDF. Please try again or contact administrator.')
                            ->danger()
                            ->send();

                        return;
                    }
                }),

            // view invoice jika ada
            Actions\Action::make('viewInvoice')
                ->label('Lihat Invoice')
                ->icon('heroicon-o-document-text')
                ->url(function ($record) {
                    $invoice = $record->transaksi?->invoices()?->first();
                    return $invoice ? route('filament.admin.resources.invoices.view', ['record' => $invoice->id]) : null;
                })
                ->visible(function ($record) {
                    return $record->transaksi?->invoices()?->exists() ?? false;
                })
                ->openUrlInNewTab(false),

            Actions\Action::make('createAllowance')
                ->label('Buat Uang Jalan')
                ->icon('heroicon-o-banknotes')
                ->url(fn($record) => route('filament.admin.resources.uang-jalans.create', ['id_do' => $record->id, 'id_user' => $record->id_user]))
                ->visible(fn($record) => !$record->uangJalan),
            Actions\Action::make('editAllowance')
                ->label('Edit Uang Jalan')
                ->icon('heroicon-o-banknotes')
                ->url(fn($record) => $record->uangJalan ? route('filament.admin.resources.uang-jalans.edit', ['record' => $record->uangJalan->id]) : null)
                ->visible(fn($record) => $record->uangJalan),
            // Hidden - Create Driver Delivery tidak digunakan
            // Actions\Action::make('createDelivery')
            //     ->label('Create Driver Delivery')
            //     ->icon('heroicon-o-truck')
            //     ->url(fn($record) => route('filament.admin.resources.pengiriman-drivers.create', ['id_do' => $record->id]))
            //     ->visible(fn($record) => !$record->pengirimanDriver),
            // view
            Actions\Action::make('viewDelivery')
                ->label('Status Pengiriman')
                ->icon('heroicon-o-truck')
                ->url(fn($record) => $record->pengirimanDriver ? route('filament.admin.resources.pengiriman-drivers.view', ['record' => $record->pengirimanDriver->id]) : null)
                ->visible(fn($record) => $record->pengirimanDriver),
            // Actions\Action::make('editDelivery')
            //     ->label('Edit Driver Delivery')
            //     ->icon('heroicon-o-truck')
            //     ->url(fn($record) => $record->pengirimanDriver ? route('filament.admin.resources.pengiriman-drivers.edit', ['record' => $record->pengirimanDriver->id]) : null)
            //     ->visible(fn($record) => $record->pengirimanDriver),

            // jika ada informasi totalisator, maka tampilkan tombol buat invoice
            Actions\Action::make('createInvoice')
                ->label('Buat Invoice')
                ->icon('heroicon-o-document-text')
                ->url(fn($record) => route('filament.admin.resources.invoices.create', ['id_transaksi' => $record->id_transaksi]))
                ->visible(function ($record) {
                    return $record->pengirimanDriver && !($record->transaksi?->invoices()?->exists() ?? false);
                }),
        ];
    }


    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Delivery Order')
                    ->schema([
                        TextEntry::make('kode')
                            ->label('Nomor DO'),

                        TextEntry::make('transaksi.nomor_transaksi')
                            ->label('Nomor Transaksi'),

                        TextEntry::make('transaksi.pelanggan.nama')
                            ->label('Pelanggan'),

                        TextEntry::make('letterSetting.name')
                            ->label('Penerbitan Surat')
                            ->placeholder('Tidak Ada')
                            ->badge()
                            ->color('info'),

                        TextEntry::make('letterSetting.locale')
                            ->label('Bahasa Surat')
                            ->placeholder('Tidak Ada')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'id' => 'Bahasa Indonesia',
                                'en' => 'English',
                                default => $state
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'id' => 'success',
                                'en' => 'warning',
                                default => 'gray'
                            }),

                        TextEntry::make('tipe_pelaksana')
                            ->label('Tipe Pelaksana')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'internal' => 'Internal (Kendaraan Sendiri)',
                                'external' => 'External (Sewa Jasa)',
                                default => $state
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'internal' => 'success',
                                'external' => 'warning',
                                default => 'gray'
                            }),

                        TextEntry::make('tipe_transportasi')
                            ->label('Tipe Transportasi')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'mobil' => 'Mobil',
                                'kapal' => 'Kapal',
                                default => $state
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'mobil' => 'info',
                                'kapal' => 'primary',
                                default => 'gray'
                            }),

                        TextEntry::make('nama_penyedia_jasa')
                            ->label('Nama Penyedia Jasa')
                            ->placeholder('Tidak Ada')
                            ->visible(fn($record) => $record->tipe_pelaksana === 'external'),

                        TextEntry::make('biaya_sewa_jasa')
                            ->label('Biaya Sewa Jasa')
                            ->money('IDR')
                            ->placeholder('Tidak Ada')
                            ->visible(fn($record) => $record->tipe_pelaksana === 'external'),

                        TextEntry::make('tanggal_delivery')
                            ->label('Tanggal Pengiriman')
                            ->dateTime('d M Y H:i'),

                        TextEntry::make('user.name')
                            ->label('Supir')
                            ->placeholder('Belum Ditugaskan')
                            ->visible(fn($record) => $record->tipe_pelaksana === 'internal' && $record->tipe_transportasi === 'mobil'),

                        TextEntry::make('kendaraan.no_pol_kendaraan')
                            ->label('Kendaraan')
                            ->placeholder('Belum Ditugaskan')
                            ->visible(fn($record) => $record->tipe_pelaksana === 'internal' && $record->tipe_transportasi === 'mobil'),

                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn(string $state): string => match ($state) {
                                'pending' => 'warning',
                                'in_progress' => 'info',
                                'completed' => 'success',
                                'cancelled' => 'danger',
                                default => 'gray',
                            }),
                    ])
                    ->columns(2),

                Section::make('Detail Items')
                    ->schema([
                        RepeatableEntry::make('details')
                            ->label('')
                            ->schema([
                                TextEntry::make('item.name')
                                    ->label('Item'),

                                TextEntry::make('volume_ordered')
                                    ->label('Volume Dipesan')
                                    ->numeric()
                                    ->suffix(' L'),

                                TextEntry::make('volume_delivered')
                                    ->label('Volume Dikirim')
                                    ->numeric()
                                    ->suffix(' L'),

                                TextEntry::make('notes')
                                    ->label('Catatan')
                                    ->placeholder('Tidak ada catatan'),
                            ])
                            ->columns(2)
                            ->columnSpanFull(),

                        TextEntry::make('volume_do')
                            ->label('Total Volume DO')
                            ->numeric()
                            ->suffix(' L')
                            ->weight('bold'),
                    ])
                    ->collapsible(),

                Section::make('Informasi Tambahan')
                    ->schema([
                        TextEntry::make('catatan')
                            ->label('Catatan')
                            ->placeholder('Tidak ada catatan'),

                        TextEntry::make('created_at')
                            ->label('Dibuat')
                            ->dateTime('d M Y H:i'),

                        TextEntry::make('updated_at')
                            ->label('Diperbarui')
                            ->dateTime('d M Y H:i'),
                    ])
                    ->columns(2)
                    ->collapsible(),
                // informasi muat
                Section::make('Informasi Muat')
                    ->schema([
                        // status
                        TextEntry::make('status_muat')
                            ->label('Status Muat')
                            ->badge()
                            ->color(fn(string $state): string => match ($state) {
                                'pending' => 'warning',
                                'muat' => 'info',
                                'selesai' => 'success',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn(string $state): string => match ($state) {
                                'pending' => 'Perintah Muat Diterbitkan',
                                'muat' => 'Muat Dikonfirmasi',
                                'selesai' => 'Muat Selesai',
                                default => $state,
                            }),
                        TextEntry::make('waktu_muat')
                            ->label('Waktu Mulai Muat')
                            ->dateTime()
                            ->icon('heroicon-o-clock')
                            ->placeholder('Belum dilaksanakan'),

                        TextEntry::make('waktu_selesai_muat')
                            ->label('Waktu Selesai Muat')
                            ->dateTime()
                            ->icon('heroicon-o-clock')
                            ->placeholder('Belum dilaksanakan'),
                    ])
                    ->columns(3)
                    ->collapsible(),

                // informasi uang jalan
                Section::make('Informasi Uang Jalan')
                    ->schema([
                        TextEntry::make('uangJalan.nominal')
                            ->label('Total Uang Jalan')
                            ->money('IDR')
                            ->placeholder('Belum Dibuat'),

                        // IconEntry::make('uangJalan.status_kirim')
                        //     ->label('Status Pengiriman')
                        //     ->boolean()
                        //     ->placeholder('Belum Dibuat'),

                        // TextEntry::make('uangJalan.tanggal_kirim')
                        //     ->label('Tanggal Pengiriman')
                        //     ->date()
                        //     ->placeholder('Belum Dibuat'),

                        IconEntry::make('uangJalan.status_terima')
                            ->label('Status Penerimaan')
                            ->boolean()
                            ->placeholder('Belum Dibuat'),

                        TextEntry::make('uangJalan.tanggal_terima')
                            ->label('Tanggal Penerimaan')
                            ->date()
                            ->placeholder('Belum Dibuat'),
                    ])
                    ->columns(3)
                    ->collapsible(),

                // breakdown uang jalan
                Section::make('Breakdown Uang Jalan')
                    ->schema([
                        TextEntry::make('uangJalan.uang_depot')
                            ->label('Uang Depot')
                            ->money('IDR')
                            ->placeholder('Rp 0'),

                        TextEntry::make('uangJalan.uang_jalan_amount')
                            ->label('Uang Jalan')
                            ->money('IDR')
                            ->placeholder('Rp 0'),

                        TextEntry::make('uangJalan.uang_bongkar')
                            ->label('Uang Bongkar')
                            ->money('IDR')
                            ->placeholder('Rp 0'),

                        TextEntry::make('uangJalan.uang_pas')
                            ->label('Uang Pas')
                            ->money('IDR')
                            ->placeholder('Rp 0'),

                        TextEntry::make('uangJalan.uang_lembur')
                            ->label('Uang Lembur')
                            ->money('IDR')
                            ->placeholder('Rp 0'),

                        TextEntry::make('uangJalan.uang_bbm')
                            ->label('Uang BBM')
                            ->money('IDR')
                            ->placeholder('Rp 0'),

                        TextEntry::make('uangJalan.uang_tol')
                            ->label('Uang Tol')
                            ->money('IDR')
                            ->placeholder('Rp 0'),
                    ])
                    ->columns(4)
                    ->visible(fn($record) => $record->uangJalan)
                    ->collapsible(),

                // informasi pengiriman
                Section::make('Informasi Pengiriman Driver')
                    ->schema([
                        TextEntry::make('pengirimanDriver.waktu_mulai')
                            ->label('Waktu Mulai')
                            ->dateTime()
                            ->icon('heroicon-o-clock')
                            ->placeholder('Belum dilaksanakan'),
                        TextEntry::make('pengirimanDriver.waktu_tiba')
                            ->label('Waktu Tiba')
                            ->dateTime()
                            ->icon('heroicon-o-clock')
                            ->placeholder('Belum dilaksanakan'),

                        TextEntry::make('pengirimanDriver.waktu_pool_arrival')
                            ->label('Waktu Kembali Pool')
                            ->dateTime()
                            ->icon('heroicon-o-clock')
                            ->placeholder('Belum dilaksanakan'),

                        // informasi totalisator
                        TextEntry::make('pengirimanDriver.totalisator_awal')
                            ->label('Totalisator Awal')
                            ->suffix(' L')
                            // icon
                            ->icon('heroicon-o-chart-bar')
                            ->numeric()
                            ->placeholder('Belum Diisi'),

                        TextEntry::make('pengirimanDriver.totalisator_tiba')
                            ->label('Totalisator Tiba')
                            ->suffix(' L')
                            // icon
                            ->icon('heroicon-o-chart-bar')
                            ->numeric()
                            ->placeholder('Belum Diisi'),

                        TextEntry::make('pengirimanDriver.totalisator_pool_return')
                            ->label('Totalisator Kembali Pool')
                            ->suffix(' L')
                            // icon
                            ->icon('heroicon-o-chart-bar')
                            ->numeric()
                            ->placeholder('Belum Diisi'),


                        // TextEntry::make('pengirimanDriver.catatan_pengiriman')
                        //     ->label('Catatan Pengiriman')
                        //     ->placeholder('No notes')
                        //     ->columnSpanFull(),
                    ])
                    ->columns(3)
                    ->collapsible(),

                // Informasi Segel
                Section::make('Informasi Segel')
                    ->schema([
                        RepeatableEntry::make('seals')
                            ->label('')
                            ->schema([
                                TextEntry::make('nomor_segel')
                                    ->label('Nomor Segel')
                                    ->weight('bold')
                                    ->icon('heroicon-o-lock-closed'),

                                TextEntry::make('jenis_segel')
                                    ->label('Jenis Segel')
                                    ->formatStateUsing(fn($state) => match ($state) {
                                        'atas' => 'Segel Atas',
                                        'bawah' => 'Segel Bawah',
                                        'samping' => 'Segel Samping',
                                        'lainnya' => 'Lainnya',
                                        default => $state
                                    })
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        'atas' => 'success',
                                        'bawah' => 'info',
                                        'samping' => 'warning',
                                        'lainnya' => 'gray',
                                        default => 'gray'
                                    }),

                                TextEntry::make('keterangan')
                                    ->label('Keterangan')
                                    ->placeholder('Tidak ada keterangan'),

                                TextEntry::make('urutan')
                                    ->label('Urutan')
                                    ->numeric()
                                    ->badge(),
                            ])
                            ->columns(4)
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record->seals->count() > 0)
                    ->collapsible(),

                // Foto Segel
                Section::make('Foto Segel')
                    ->schema([
                        \Filament\Infolists\Components\ViewEntry::make('seals')
                            ->label('')
                            ->view('components.delivery-order-seal-photos')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record->seals->filter(fn($seal) => $seal->getFirstMedia('foto_segel'))->count() > 0)
                    ->collapsible(),

                // Status Pengiriman dengan Gambar
                Section::make('Status Pengiriman & Bukti Foto')
                    ->schema([
                        TextEntry::make('pengirimanDriver.approval_status')
                            ->label('Status Approval')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'pending' => 'Menunggu Approval',
                                'approved' => 'Disetujui',
                                'rejected' => 'Ditolak',
                                default => 'Belum Ada'
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'pending' => 'warning',
                                'approved' => 'success',
                                'rejected' => 'danger',
                                default => 'gray'
                            }),

                        TextEntry::make('pengirimanDriver.approved_by.name')
                            ->label('Disetujui Oleh')
                            ->placeholder('Belum Disetujui'),

                        TextEntry::make('pengirimanDriver.approved_at')
                            ->label('Tanggal Approval')
                            ->dateTime('d M Y H:i')
                            ->placeholder('Belum Disetujui'),

                        TextEntry::make('pengirimanDriver.approval_notes')
                            ->label('Catatan Approval')
                            ->placeholder('Tidak ada catatan')
                            ->columnSpanFull(),
                    ])
                    ->columns(3)
                    ->visible(fn($record) => $record->pengirimanDriver)
                    ->collapsible(),

                // Bukti Foto Pengiriman
                Section::make('Bukti Foto Pengiriman')
                    ->schema([
                        // Custom component untuk menampilkan foto-foto
                        \Filament\Infolists\Components\ViewEntry::make('pengirimanDriver')
                            ->label('')
                            ->view('components.delivery-order-photos')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record->pengirimanDriver && $record->pengirimanDriver->getMedia()->count() > 0)
                    ->collapsible(),
            ]);
    }
}
